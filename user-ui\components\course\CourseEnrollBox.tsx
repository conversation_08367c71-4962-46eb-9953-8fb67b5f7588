
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { BookOpen, Award, Briefcase, TrendingUp, CheckCircle, FileText, Star, User } from 'lucide-react';

interface CourseEnrollBoxProps {
  price: number | string;
  students: number;
  features: string[];
  handleEnroll: () => void;
}

const CourseEnrollBox: React.FC<CourseEnrollBoxProps> = ({ 
  price, 
  students, 
  features, 
  handleEnroll 
}) => {
  return (
    <Card className="border border-indigo-200 rounded-lg bg-white shadow-md hover:shadow-lg transition-shadow">
      <CardContent className="p-5 space-y-4">
        <div className="text-2xl font-bold mb-3 text-primary">{price === 0 ? "Free" : `$${price}`}</div>
        
        <div className="flex items-center gap-2 text-sm text-green-700 font-medium my-2 bg-green-50 p-2 rounded-md border border-green-100">
          <TrendingUp className="h-4 w-4" />
          <span>Join {students.toLocaleString()}+ successful learners</span>
        </div>
        
        <Button variant="default" size="lg" className="w-full mt-4 bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white" onClick={handleEnroll}>
          <BookOpen className="mr-2 h-5 w-5" />
          Enroll Now - Start Learning Today
        </Button>
        
        <div className="pt-4 border-t border-indigo-100">
          <h3 className="font-medium mb-2 flex items-center gap-2">
            <Award className="h-4 w-4 text-amber-500" />
            <span className="text-gray-800">This course includes:</span>
          </h3>
          <ul className="space-y-2">
            {features.map((feature, index) => (
              <li key={index} className="flex items-start text-sm">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700">{feature}</span>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="pt-4 border-t border-indigo-100">
          <h3 className="font-medium mb-3 flex items-center gap-2 text-gray-800">
            <Star className="h-4 w-4 text-amber-500" />
            <span>Career Benefits:</span>
          </h3>
          <div className="space-y-3">
            <div className="flex items-start text-sm">
              <div className="w-7 h-7 rounded-full bg-blue-100 flex items-center justify-center mr-3 flex-shrink-0 border border-blue-200">
                <Award className="h-4 w-4 text-blue-600" />
              </div>
              <span className="text-gray-700 font-medium">Course Completion Certificate</span>
            </div>
            <div className="flex items-start text-sm">
              <div className="w-7 h-7 rounded-full bg-amber-100 flex items-center justify-center mr-3 flex-shrink-0 border border-amber-200">
                <Briefcase className="h-4 w-4 text-amber-600" />
              </div>
              <span className="text-gray-700 font-medium">Internship Opportunity</span>
            </div>
            <div className="flex items-start text-sm">
              <div className="w-7 h-7 rounded-full bg-purple-100 flex items-center justify-center mr-3 flex-shrink-0 border border-purple-200">
                <User className="h-4 w-4 text-purple-600" />
              </div>
              <span className="text-gray-700 font-medium">Career Placement Support</span>
            </div>
            <div className="flex items-start text-sm">
              <div className="w-7 h-7 rounded-full bg-orange-100 flex items-center justify-center mr-3 flex-shrink-0 border border-orange-200">
                <FileText className="h-4 w-4 text-orange-500" />
              </div>
              <span className="text-gray-700 font-medium">UpskillEO Portfolio</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CourseEnrollBox;
