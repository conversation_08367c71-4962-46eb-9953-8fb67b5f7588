const mongoose = require('mongoose');

const courseSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  longDescription: {
    type: String,
    required: true,
    trim: true
  },
  badge: {
    type: String,
    trim: true
  },
  totalLessons: {
    type: Number,
    required: true,
    min: 0
  },
  level: {
    type: String,
    required: true,
    enum: ['Beginner', 'Intermediate', 'Advanced', 'Beginner to Advanced', 'Beginner to Expert']
  },
  projects: {
    type: Number,
    required: true,
    min: 0
  },
  image: {
    type: String,
    required: true,
    trim: true
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  instructor: {
    type: String,
    required: true,
    trim: true
  },
  rating: {
    type: Number,
    required: true,
    min: 0,
    max: 5,
    default: 0
  },
  reviews: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  students: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  lastUpdated: {
    type: String,
    required: true,
    trim: true
  },
  features: [{
    type: String,
    trim: true
  }],
  topics: [{
    type: String,
    trim: true
  }],
  resources: [{
    link: {
      type: String,
      required: true,
      trim: true
    },
    filename: {
      type: String,
      required: true,
      trim: true
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  collection: 'courses'
});

// Index for better query performance
courseSchema.index({ slug: 1 });
courseSchema.index({ id: 1 });
courseSchema.index({ instructor: 1 });
courseSchema.index({ level: 1 });
courseSchema.index({ isActive: 1 });

// Virtual for formatted price
courseSchema.virtual('formattedPrice').get(function() {
  return `$${this.price.toFixed(2)}`;
});

// Method to get course statistics
courseSchema.methods.getStats = function() {
  return {
    totalStudents: this.students,
    averageRating: this.rating,
    totalReviews: this.reviews,
    completionRate: this.students > 0 ? (this.students / this.totalLessons) : 0
  };
};

// Static method to find courses by level
courseSchema.statics.findByLevel = function(level) {
  return this.find({ level, isActive: true });
};

// Static method to find courses by instructor
courseSchema.statics.findByInstructor = function(instructor) {
  return this.find({ instructor, isActive: true });
};

// Pre-save middleware to update the updatedAt field
courseSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

module.exports = mongoose.model('Course', courseSchema); 