const jwt = require('jsonwebtoken');

const JWT_SECRET = 'your_secret_key'; // Use env var in real apps

// Create a token
function createToken(user) {
  const payload = {
    email: user.email,
    id: user.id || 'dummy-id',
  };

  return jwt.sign(payload, JWT_SECRET, { expiresIn: '360d' });
}


function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (err) {
    return null;
  }
}


function authenticateUser(req, res, next) {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'Authorization header missing or invalid' });
  }

  const token = authHeader.split(' ')[1];
  const decoded = verifyToken(token);

  if (!decoded) {
    return res.status(401).json({ message: 'Invalid or expired token' });
  }

  // Attach user to request object
  req.user = decoded;

  next();
}

module.exports = {
  createToken,
  authenticateUser,
};