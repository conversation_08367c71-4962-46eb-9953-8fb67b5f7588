// API Response Types for Learning Platform

export interface LoginResponse {
  message: string;
  accessToken: string;
  user?: {
    id: string;
    email: string;
    name: string;
    enrolledCoursesCount: number;
    enrolledCourseIds: string[];
  };
}

export interface Course {
  id: number;
  title: string;
  description: string;
  badge?: string;
  totalLessons: number;
  level?: string;
  projects?: number;
  image?: string;
  color?: string;
  features?: string[];
  resources?: Array<{ link: string; filename: string }>;
  modules: Array<{
    id: string;
    title: string;
    description: string;
    totalHours: number;
    totalLessons: number;
    labels?: Array<{ title: string; type: string }>;
    lessons: Array<{
      id: string;
      title: string;
      description: string;
      duration: string;
      videoUrl: string;
      breakpoints: Array<{
        id: string;
        openAtDuration: number;
        challengeType: string;
        editorSettings: Array<{
          type: string;
          boilerplateCode: string;
        }>;
      }>;
    }>;
  }>;
}

export interface CourseDetails {
  enrolled: boolean;
  title?: string;
  description?: string;
  progress?: number;
  completedLessons?: number;
  totalLessons?: number;
  resources?: Array<{ link: string; filename: string }>;
  modules?: Array<{
    title: string;
    description: string;
    progress: number;
    totalHours: number;
    totalLessons: number;
    labels?: Array<{ title: string; type: string }>;
    lessons: Array<{
      id: string;
      title: string;
      completedDuration: string;
      duration: string;
      completed: boolean;
      locked: boolean;
    }>;
  }>;
}

export interface ModuleDetails {
  id: string;
  title: string;
  description: string;
  labels: Array<{ title: string; type: string }>;
  lastWatchedLessonId: string | null;
  courseName: string;
  moduleNumber: number;
  sections: Array<{
    id: string;
    title: string;
    description: string;
    status: string;
    locked: boolean;
    lessons: Array<{
      id: string;
      description: string;
      status: string;
      totalDuration: string;
      videoUrl: string;
      continueAtDuration: number;
      locked: boolean;
      breakpoints: Array<{
        id: string;
        openAtDuration: number;
        challengeType: string;
        completed: boolean;
        editorSettings: Array<{
          type: string;
          boilerplateCode: string;
          userCode: string;
        }>;
      }>;
    }>;
  }>;
  courseId: string | null;
}

export interface LessonProgressRequest {
  duration: number;
}

export interface BreakpointRequest {
  completed: boolean;
  editorSettings: Array<{ type: string; userCode: string }>;
}

export interface ApiResponse<T = unknown> {
  message?: string;
  data?: T;
  error?: string;
}
