
import React from 'react';
import { Info } from 'lucide-react';

interface HintsSectionProps {
  hints: string[];
  show: boolean;
}

const HintsSection: React.FC<HintsSectionProps> = ({ hints, show }) => {
  if (!show || hints.length === 0) return null;

  return (
    <div className="bg-amber-500/10 border-amber-500/30 border-b px-4 py-3 text-sm animate-in fade-in-50 slide-in-from-top-5">
      <div className="flex items-start">
        <Info className="h-5 w-5 text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
        <div>
          <p className="font-medium text-amber-600 mb-1">Hints:</p>
          <ul className="list-disc list-inside space-y-1 text-muted-foreground">
            {hints.map((hint, index) => (
              <li key={index}>{hint}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default HintsSection;
