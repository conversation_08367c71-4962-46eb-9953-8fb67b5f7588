{"name": "user-ui-backend", "version": "1.0.0", "description": "Backend API for User UI learning platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test/testConnection.js", "seed": "node scripts/seedCourses.js", "seed:stats": "node scripts/seedCourses.js stats", "db:connect": "node -e \"require('./config/database').connectDB()\"", "db:test": "node test/testConnection.js"}, "keywords": ["nodejs", "express", "mongodb", "mongoose", "api", "learning-platform"], "author": "User UI Team", "license": "MIT", "dependencies": {"mongoose": "^7.6.3", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}