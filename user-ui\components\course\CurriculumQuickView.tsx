"use client";

import React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import {
  Clock,
  BookOpen,
  CheckCircle,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  BarChart,
  Play,
  Lock,
  ArrowRight,
  Award,
} from "lucide-react";
import { CourseModule } from "@/types/course";
import ModuleProgressBar from "./ModuleProgressBar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CurriculumQuickViewProps {
  modules: CourseModule[];
}

const CurriculumQuickView: React.FC<CurriculumQuickViewProps> = ({
  modules,
}) => {
  const router = useRouter();

  // Initialize openSections state without URL parameters
  const [openSections, setOpenSections] = React.useState<
    Record<string, boolean>
  >({});

  // State for tooltip visibility
  const [tooltipOpen, setTooltipOpen] = React.useState<Record<string, boolean>>(
    {}
  );

  const toggleSection = (moduleId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [moduleId]: !prev[moduleId],
    }));
  };

  // Handle locked lesson click
  const handleLockedLessonClick = (lessonId: string) => {
    setTooltipOpen((prev) => ({
      ...prev,
      [lessonId]: true,
    }));

    // Auto close after 3 seconds
    setTimeout(() => {
      setTooltipOpen((prev) => ({
        ...prev,
        [lessonId]: false,
      }));
    }, 3000);
  };

  // Toggle all modules expansion
  const toggleAllSections = () => {
    // Check if all modules are currently expanded
    const allExpanded = modules.every((module) => openSections[module.id]);

    const newState: Record<string, boolean> = {};
    modules.forEach((module) => {
      newState[module.id] = !allExpanded;
    });

    setOpenSections(newState);
  };

  return (
    <div className="space-y-8 animate-in fade-in-50 duration-300">
      {/* Toggle All Button */}
      <div className="flex justify-end mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={toggleAllSections}
          className="text-xs flex items-center gap-1"
        >
          {modules.every((module) => openSections[module.id]) ? (
            <>
              <ChevronUp className="h-3 w-3" />
              Collapse All
            </>
          ) : (
            <>
              <ChevronDown className="h-3 w-3" />
              Expand All
            </>
          )}
        </Button>
      </div>

      {/* Progress Timeline */}
      <div className="relative">
        {/* Base timeline */}
        <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-muted"></div>

        {modules.map((module, index) => {
          const isLastModule = index === modules.length - 1;

          // Calculate progress for each module
          let currentProgress = 0;
          if (typeof module.progress === "number") {
            currentProgress = module.progress;
          } else if (module.sections && module.sections.length > 0) {
            const completedSections = module.sections.filter(
              (s) => s.completed
            ).length;
            currentProgress = Math.round(
              (completedSections / module.sections.length) * 100
            );
          }

          return (
            <div key={module.id} className="relative pl-12 pb-8">
              {/* Timeline connector */}
              {!isLastModule && (
                <div className="absolute left-4 top-8 bottom-0 w-0.5">
                  <div
                    className={`h-full w-full ${
                      currentProgress === 100
                        ? "bg-green-500"
                        : currentProgress > 0
                        ? "bg-primary"
                        : "bg-muted"
                    }`}
                    style={{
                      height: `${currentProgress}%`,
                    }}
                  ></div>
                </div>
              )}

              {/* Milestone marker */}
              <div
                className={`absolute left-4 -translate-x-1/2 w-8 h-8 rounded-full flex items-center justify-center z-10 
                ${
                  currentProgress === 100
                    ? "bg-green-200/80 text-green-700/90 ring-2 ring-green-100/40"
                    : currentProgress > 0
                    ? "bg-primary text-white ring-4 ring-primary/20"
                    : "bg-muted border-2 border-primary/30 text-muted-foreground"
                }`}
              >
                {currentProgress === 100 ? (
                  <Award size={16} />
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </div>

              {/* Module card */}
              <Card
                className={`overflow-hidden transition-all duration-300 hover:shadow-md 
                  ${
                    currentProgress === 100
                      ? "border-green-200/50 dark:border-green-800/30 bg-green-50/30 dark:bg-green-900/5 hover:border-green-200/70 dark:hover:border-green-800/40"
                      : currentProgress > 0
                      ? "border-primary/30 bg-primary/[0.025] hover:border-primary/40"
                      : "border-gray-200/50 dark:border-gray-700/50 bg-gray-50/30 dark:bg-gray-800/20 hover:bg-gray-100/40 dark:hover:bg-gray-800/30"
                  }`}
              >
                <div className="p-4">
                  {/* Module Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 pr-20">
                      <h3 className="font-medium text-base mb-1">
                        {module.title}
                      </h3>
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {module.description}
                      </p>
                    </div>
                    <Badge
                      variant={
                        currentProgress === 100
                          ? "outline"
                          : currentProgress > 0
                          ? "secondary"
                          : "outline"
                      }
                      className={`${
                        currentProgress === 100
                          ? "bg-green-50/50 dark:bg-green-900/10 text-green-600/90 dark:text-green-400/90 border-green-200/50 dark:border-green-800/30"
                          : currentProgress > 0
                          ? "bg-primary/10 text-primary"
                          : "bg-gray-50/40 dark:bg-gray-800/30 text-gray-600/80 dark:text-gray-400/80 border-gray-200/50 dark:border-gray-700/50"
                      }`}
                    >
                      {currentProgress === 100
                        ? "Completed"
                        : currentProgress > 0
                        ? `${Math.round(currentProgress)}%`
                        : "Not started"}
                    </Badge>
                  </div>

                  {/* Module Stats */}
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-1.5">
                      <Clock className="h-4 w-4" />
                      <span>{module.duration}</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <BookOpen className="h-4 w-4" />
                      <span>{module.sections?.length || 0} lessons</span>
                    </div>
                    {module.sections && (
                      <div className="flex items-center gap-1.5">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>
                          {module.sections.filter((s) => s.completed).length}{" "}
                          lessons done
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Module Progress */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-xs mb-1.5">
                      <span className="text-muted-foreground">
                        Module Progress
                      </span>
                      <span className="font-medium">
                        {Math.round(currentProgress)}%
                      </span>
                    </div>
                    <div className="relative h-1.5 bg-muted rounded-full overflow-hidden">
                      <div
                        className={`absolute inset-y-0 left-0 transition-all duration-500 ${
                          currentProgress === 100
                            ? "bg-green-400/70"
                            : currentProgress > 0
                            ? "bg-primary"
                            : "bg-muted"
                        }`}
                        style={{ width: `${currentProgress}%` }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
                    </div>
                  </div>

                  {/* Module Content */}
                  <Collapsible
                    open={openSections[module.id] || false}
                    onOpenChange={() => toggleSection(module.id)}
                  >
                    <div className="flex items-center justify-between gap-3 mb-4">
                      <Button
                        variant="outline"
                        size="sm"
                        className={`flex items-center gap-2 hover:bg-muted/80`}
                        onClick={() => toggleSection(module.id)}
                      >
                        <BookOpen className="h-4 w-4" />
                        {openSections[module.id]
                          ? "Hide Lessons"
                          : "View Lessons"}
                        <ChevronDown
                          className={`h-4 w-4 transition-transform duration-200 ${
                            openSections[module.id] ? "rotate-180" : ""
                          }`}
                        />
                      </Button>

                      <Button
                        size="sm"
                        variant={
                          currentProgress === 100 ? "outline" : "default"
                        }
                        className={`${
                          currentProgress === 100
                            ? "bg-green-50/50 dark:bg-green-900/10 text-green-600/90 dark:text-green-400/90 border-green-200/50 dark:border-green-800/30 hover:bg-green-50/70 dark:hover:bg-green-900/20"
                            : currentProgress === 0
                            ? "border-gray-200/50 dark:border-gray-700/50 bg-gray-50/30 dark:bg-gray-800/20 text-gray-600/80 dark:text-gray-400/80 hover:bg-gray-100/40 dark:hover:bg-gray-800/30"
                            : "bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white shadow-md hover:scale-105 border-none"
                        }`}
                        onClick={() => router.push(`/module/${module.id}`)}
                        disabled={module.locked}
                      >
                        {currentProgress === 100 ? (
                          <>
                            <ArrowRight className="h-4 w-4 mr-1" />
                            Review
                          </>
                        ) : currentProgress > 0 ? (
                          <>
                            <Play className="h-4 w-4 mr-1" />
                            Continue
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-1" />
                            Start
                          </>
                        )}
                      </Button>
                    </div>

                    <CollapsibleContent className="space-y-4 animate-in fade-in-50 slide-in-from-top-2">
                      {/* Module Tags */}
                      {(module.badge || module.track || module.topics) && (
                        <div className="mb-4 flex flex-wrap gap-1.5">
                          {module.badge && (
                            <Badge
                              variant="outline"
                              className="bg-primary/10 text-primary border-primary/30"
                            >
                              {module.badge}
                            </Badge>
                          )}

                          {module.track && (
                            <Badge
                              variant="outline"
                              className="bg-secondary/10 text-secondary-foreground border-secondary/30"
                            >
                              {module.track}
                            </Badge>
                          )}

                          {module.topics &&
                            module.topics.length > 0 &&
                            module.topics.map((topic, i) => (
                              <Badge
                                key={i}
                                variant="outline"
                                className="bg-muted/50 text-xs border-muted/80"
                              >
                                {topic}
                              </Badge>
                            ))}
                        </div>
                      )}

                      {/* Lessons List */}
                      {module.sections && module.sections.length > 0 && (
                        <div className="space-y-2 pt-4 border-t">
                          <div className="space-y-2">
                            {module.sections.map((section) => (
                              <div key={section.id} className="space-y-2">
                                {/* Section Header */}
                                <div className="flex items-center gap-1">
                                  <span className="text-[10px] text-muted-foreground/70 font-medium">
                                    {section.title}
                                  </span>
                                  <div className="flex-1 h-px bg-border/50"></div>
                                </div>
                                {/* Section Lessons */}
                                {section.lessons &&
                                  section.lessons.map((lesson) => (
                                    <TooltipProvider key={lesson.id}>
                                      <Tooltip
                                        open={
                                          lesson.locked
                                            ? tooltipOpen[lesson.id]
                                            : undefined
                                        }
                                      >
                                        <TooltipTrigger asChild>
                                          <div
                                            className={`pl-4 pr-3 py-2 rounded-md flex items-center justify-between
                                            ${
                                              lesson.completed
                                                ? "bg-green-500/5 border border-green-500/20 hover:bg-green-200/10 cursor-pointer"
                                                : lesson.locked
                                                ? "bg-muted/30 hover:bg-muted/40 border border-border/30 cursor-pointer"
                                                : "bg-muted/50 hover:bg-muted/70 border border-border cursor-pointer"
                                            }
                                            transition-colors`}
                                            onClick={() => {
                                              if (lesson.locked) {
                                                handleLockedLessonClick(
                                                  lesson.id
                                                );
                                              } else {
                                                router.push(
                                                  `/module/${module.id}`
                                                );
                                              }
                                            }}
                                          >
                                            <div className="flex items-center gap-2">
                                              <div
                                                className={`w-5 h-5 rounded-full flex items-center justify-center ${
                                                  lesson.completed
                                                    ? "bg-green-500 text-white"
                                                    : lesson.locked
                                                    ? "bg-muted text-muted-foreground"
                                                    : "bg-primary/10 text-primary"
                                                }`}
                                              >
                                                {lesson.completed ? (
                                                  <CheckCircle size={12} />
                                                ) : lesson.locked ? (
                                                  <Lock size={12} />
                                                ) : (
                                                  <Play size={12} />
                                                )}
                                              </div>
                                              <span className="text-sm font-medium">
                                                {lesson.title}
                                              </span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                              <span className="text-xs text-muted-foreground">
                                                {lesson.duration &&
                                                !lesson.duration.includes("min")
                                                  ? `${lesson.duration} min`
                                                  : lesson.duration}
                                              </span>
                                              {!lesson.locked && (
                                                <Button
                                                  variant="ghost"
                                                  size="icon"
                                                  className="h-6 w-6 opacity-80 hover:opacity-100"
                                                >
                                                  <Play size={12} />
                                                </Button>
                                              )}
                                            </div>
                                          </div>
                                        </TooltipTrigger>
                                        {lesson.locked && (
                                          <TooltipContent>
                                            <p>
                                              Complete previous lessons to
                                              unlock this content
                                            </p>
                                          </TooltipContent>
                                        )}
                                      </Tooltip>
                                    </TooltipProvider>
                                  ))}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CollapsibleContent>
                  </Collapsible>
                </div>
              </Card>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CurriculumQuickView;
