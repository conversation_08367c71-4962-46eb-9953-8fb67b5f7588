@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 6%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 252 80% 75%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 268 33% 54%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased overflow-x-hidden;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  html {
    @apply scroll-smooth;
  }
}

@layer utilities {
  .glass-morphism {
    @apply backdrop-blur-md bg-black/40 border border-white/10;
  }

  .text-gradient {
    @apply bg-gradient-to-br from-upskilleo-purple via-secondary to-upskilleo-deep-purple bg-clip-text text-transparent;
  }
}

.code-editor-container {
  @apply bg-upskilleo-dark-purple text-white rounded-lg shadow-lg p-4 overflow-hidden;
  font-family: "Fira Code", monospace;
}

.code-line {
  @apply flex;
}

.line-number {
  @apply text-gray-500 w-8 text-right pr-4;
}

.progress-indicator {
  position: relative;
  height: 8px;
  background-color: rgba(156, 163, 175, 0.2);
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #9b87f5 0%, #7e69ab 100%);
  transition: width 0.5s ease;
}

.shine-effect {
  position: relative;
  overflow: hidden;
}

.shine-effect::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: shine 3s infinite linear;
}

@keyframes shine {
  from {
    transform: translateX(-100%) rotate(45deg);
  }
  to {
    transform: translateX(100%) rotate(45deg);
  }
}

.my-edu-text {
  font-family: var(--font-edu-arrow);
}

@keyframes blob-move {
  0%,
  100% {
    transform: scale(1) translate(0, 0);
    filter: blur(0px);
  }
  33% {
    transform: scale(1.08, 0.92) translate(10px, -8px);
    filter: blur(2px);
  }
  66% {
    transform: scale(0.95, 1.05) translate(-12px, 12px);
    filter: blur(1px);
  }
}
@keyframes blob-move2 {
  0%,
  100% {
    transform: scale(1) translate(0, 0);
  }
  25% {
    transform: scale(1.1, 0.9) translate(-12px, 8px);
  }
  50% {
    transform: scale(0.92, 1.08) translate(16px, -10px);
  }
  75% {
    transform: scale(1.05, 0.95) translate(-8px, 14px);
  }
}
@keyframes blob-move3 {
  0%,
  100% {
    transform: scale(1) translate(0, 0);
  }
  20% {
    transform: scale(1.07, 0.93) translate(8px, 10px);
  }
  60% {
    transform: scale(0.93, 1.07) translate(-10px, -12px);
  }
  80% {
    transform: scale(1.03, 0.97) translate(12px, 8px);
  }
}

.animate-blob-move {
  animation: blob-move 16s ease-in-out infinite;
}
.animate-blob-move2 {
  animation: blob-move2 22s ease-in-out infinite;
}
.animate-blob-move3 {
  animation: blob-move3 18s ease-in-out infinite;
}

@keyframes sparkle1 {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) scale(1.2);
    opacity: 1;
  }
}
@keyframes sparkle2 {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(8px) scale(0.9);
    opacity: 1;
  }
}
@keyframes sparkle3 {
  0%,
  100% {
    transform: translateX(0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateX(12px) scale(1.3);
    opacity: 1;
  }
}
@keyframes sparkle4 {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-14px) scale(1.1);
    opacity: 1;
  }
}
@keyframes sparkle5 {
  0%,
  100% {
    transform: translateX(0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateX(-10px) scale(1.2);
    opacity: 1;
  }
}

.animate-sparkle1 {
  animation: sparkle1 3.5s ease-in-out infinite;
}
.animate-sparkle2 {
  animation: sparkle2 4.2s ease-in-out infinite;
}
.animate-sparkle3 {
  animation: sparkle3 3.8s ease-in-out infinite;
}
.animate-sparkle4 {
  animation: sparkle4 4.5s ease-in-out infinite;
}
.animate-sparkle5 {
  animation: sparkle5 4.1s ease-in-out infinite;
}

.upskilleo-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #9b87f5 #18122b;
}
.upskilleo-scrollbar::-webkit-scrollbar {
  width: 1px;
  background: transparent;
}
.upskilleo-scrollbar::-webkit-scrollbar-thumb {
  background: #ede9fe;
  border-radius: 8px;
  border: 1px solid #18122b;
}
.upskilleo-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
@media (max-width: 768px) {
  .upskilleo-scrollbar,
  .upskilleo-scrollbar::-webkit-scrollbar {
    display: none;
  }
}
