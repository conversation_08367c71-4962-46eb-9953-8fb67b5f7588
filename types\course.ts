export interface CourseSection {
  id: string;
  title: string;
  duration: string;
  completed: boolean;
  locked: boolean;
  videoUrl?: string;
  description?: string;
  lessons?: CourseLesson[];
}

export interface CourseBreakpoint {
  id: string;
  openAtDuration: number;
  challengeType: string;
  editorSettings?: Array<{ type: string; boilerplateCode: string }>;
}

export interface CourseLesson {
  id: string;
  title: string;
  description: string;
  duration: string;
  videoUrl?: string;
  breakpoints?: CourseBreakpoint[];
  locked?: boolean;
  completed?: boolean;
  status?: string;
  continueAtDuration?: number;
}

export interface CourseModule {
  id: string;
  title: string;
  description: string;
  duration: string;
  lessons: CourseLesson[];
  totalLessons: number;
  completed: boolean;
  locked: boolean;
  progress: number;
  badge?: string;
  track?: string;
  topics?: string[];
  sections?: CourseSection[];
}

export interface Course {
  id: string;
  title: string;
  description: string;
  modules: CourseModule[];
  imageUrl?: string;
}

export interface PurchasedCourseProps {
  courseId: string;
  courseTitle: string;
  courseDescription: string;
  courseImage: string;
  modules: CourseModule[];
}
