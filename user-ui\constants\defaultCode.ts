export const DEFAULT_HTML_CODE = `<!DOCTYPE html>
<html>
<head>
    <title>My Web Page</title>
</head>
<body>
    <h1>Hello World!</h1>
    <p>Welcome to my website.</p>
    <div id="app">
        <!-- Your content here -->
    </div>
</body>
</html>`;

export const DEFAULT_CSS_CODE = `/* Your CSS styles here */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f0f0f0;
}

h1 {
    color: #333;
    text-align: center;
}

p {
    color: #666;
    line-height: 1.6;
}

#app {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}`;

export const DEFAULT_JS_CODE = `// Your JavaScript code here
console.log("Hello from JavaScript!");

// Example function
function greetUser(name) {
    return "Hello, " + name + "!";
}

// Example event listener
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM is ready!");
    
    // You can add your interactive features here
    const app = document.getElementById('app');
    if (app) {
        app.innerHTML += '<p>JavaScript is working!</p>';
    }
});`;

// Default code object for easy access
export const DEFAULT_CODE = {
  html: DEFAULT_HTML_CODE,
  css: DEFAULT_CSS_CODE,
  js: DEFAULT_JS_CODE,
};
