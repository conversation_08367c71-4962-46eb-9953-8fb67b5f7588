
import React from 'react';
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, ArrowRight } from 'lucide-react';
import { Button } from "@/components/ui/button";

interface CourseFeedbackProps {
  code: string;
  expectedSolution: string;
  onClose: () => void;
  onSkip?: () => void;
}

const CourseFeedback: React.FC<CourseFeedbackProps> = ({ 
  code, 
  expectedSolution, 
  onClose,
  onSkip 
}) => {
  // In a real app, this would use a more sophisticated comparison
  // or potentially call an API for AI-based feedback
  const isCorrect = code.includes(expectedSolution.trim());
  
  return (
    <div className="mt-4 space-y-4 animate-in fade-in-50">
      {isCorrect ? (
        <Alert className="bg-green-500/10 border-green-500/30">
          <CheckCircle2 className="h-5 w-5 text-green-500" />
          <AlertTitle className="text-green-500">Correct Solution!</AlertTitle>
          <AlertDescription>
            Great job! Your solution meets all the requirements for this challenge.
          </AlertDescription>
        </Alert>
      ) : (
        <Alert className="bg-amber-500/10 border-amber-500/30">
          <CheckCircle2 className="h-5 w-5 text-amber-500" />
          <AlertTitle className="text-amber-500">Solution Submitted</AlertTitle>
          <AlertDescription>
            Your solution has been submitted for review.
          </AlertDescription>
        </Alert>
      )}
      
      <div className="flex justify-end">
        <Button onClick={onClose}>
          {isCorrect ? 'Continue' : 'Continue'}
        </Button>
      </div>
    </div>
  );
};

export default CourseFeedback;
