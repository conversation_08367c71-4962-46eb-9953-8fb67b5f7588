
import React, { useRef, useEffect } from 'react';
import Editor from "@monaco-editor/react";

interface CodeEditorProps {
  language: string;
  code: string;
  onChange: (value: string | undefined) => void;
  autoFocus?: boolean;
  onCodeChange?: () => void; // Callback for when code changes
  isFullscreen?: boolean; // Add isFullscreen prop
}

const CodeEditor: React.FC<CodeEditorProps> = ({ 
  language, 
  code, 
  onChange,
  autoFocus = false,
  onCodeChange,
  isFullscreen = false // Default to false
}) => {
  const editorRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Handle editor mounting
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
    if (autoFocus) {
      // Focus and move cursor to end of document
      editor.focus();
      const model = editor.getModel();
      const lastLineNumber = model.getLineCount();
      const lastLineLength = model.getLineContent(lastLineNumber).length;
      editor.setPosition({ lineNumber: lastLineNumber, column: lastLineLength + 1 });
    }
  };

  // Focus the editor when autoFocus changes
  useEffect(() => {
    if (autoFocus && editorRef.current) {
      const editor = editorRef.current;
      editor.focus();
      const model = editor.getModel();
      const lastLineNumber = model.getLineCount();
      const lastLineLength = model.getLineContent(lastLineNumber).length;
      editor.setPosition({ lineNumber: lastLineNumber, column: lastLineLength + 1 });
    }
  }, [autoFocus]);

  // Auto scroll into view when isFullscreen changes to true
  useEffect(() => {
    if (isFullscreen && containerRef.current) {
      containerRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [isFullscreen]);

  // Handle code changes with notification
  const handleCodeChange = (value: string | undefined) => {
    onChange(value);
    // Notify parent component of code change
    if (onCodeChange) {
      onCodeChange();
    }
  };

  // Map language prop to Monaco Editor language
  const getMonacoLanguage = (lang: string) => {
    const languageMap: Record<string, string> = {
      javascript: "javascript",
      js: "javascript",
      typescript: "typescript",
      ts: "typescript",
      html: "html",
      css: "css",
      python: "python",
      jsx: "javascript", // Use javascript for React/JSX with JSX option enabled
      react: "javascript", // Use javascript for React/JSX with JSX option enabled
      javascriptreact: "javascript"
    };
    return languageMap[lang.toLowerCase()] || "javascript";
  };

  // Setup editor options for JSX/React
  const getEditorOptions = () => {
    const baseOptions = {
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      fontSize: 14,
      tabSize: 2,
      automaticLayout: true,
      wordWrap: "on" as const,
      copyWithSyntaxHighlighting: true,
      lineNumbers: "on" as const,
      renderLineHighlight: "all" as const,
      matchBrackets: "always" as const,
      autoClosingBrackets: "always" as const,
      autoClosingQuotes: "always" as const,
      folding: true,
      suggest: {
        showMethods: true,
        showFunctions: true,
        showConstructors: true,
        showFields: true,
        showVariables: true,
        showClasses: true,
        showStructs: true,
        showInterfaces: true,
        showModules: true
      }
    };
    
    // Add JSX support for React files
    if (language.toLowerCase() === 'jsx' || language.toLowerCase() === 'react' || language.toLowerCase() === 'javascriptreact') {
      return {
        ...baseOptions,
        // These are special JSX options
        'javascript.validate.enable': true,
        'jsx.enabled': true
      };
    }
    
    return baseOptions;
  };

  return (
    <div ref={containerRef}>
      <Editor
        height={isFullscreen ? "70vh" : "400px"}
        language={getMonacoLanguage(language)}
        value={code}
        onChange={handleCodeChange}
        theme="vs-dark"
        onMount={handleEditorDidMount}
        options={getEditorOptions()}
      />
    </div>
  );
};

export default CodeEditor;
