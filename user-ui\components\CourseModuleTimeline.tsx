import React from "react";
import { Check, Lock } from "lucide-react";
import { CheckCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { CourseModule } from "@/types/course";

interface CourseModuleTimelineProps {
  modules: CourseModule[];
  currentModuleIndex: number;
  currentSectionIndex: number;
  currentLessonIndex: number;
  onSelectLesson: (
    moduleIndex: number,
    sectionIndex: number,
    lessonIndex: number,
    videoUrl: string
  ) => void;
}

const CourseModuleTimeline: React.FC<CourseModuleTimelineProps> = ({
  modules,
  currentModuleIndex,
  currentSectionIndex,
  currentLessonIndex,
  onSelectLesson,
}) => {
  const module = modules[currentModuleIndex];
  if (!module) return null;

  return (
    <div className="relative animate-in fade-in-50">
      {/* Base timeline line */}
      <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-muted z-0" />
      <div className="relative z-10">
        {module.sections?.map((section, sectionIndex) => {
          const isLastSection =
            sectionIndex === (module.sections?.length ?? 0) - 1;
          // Section is completed if all lessons are completed (robust check)
          const isCompleted =
            section.lessons && section.lessons.length > 0
              ? section.lessons.every(
                  (l: any) => l.completed === true || l.status === "completed"
                )
              : false;
          const isCurrent = sectionIndex === currentSectionIndex;
          // Calculate section progress %
          let sectionProgress = 0;
          if (section.lessons && section.lessons.length > 0) {
            const completedLessons = section.lessons.filter(
              (l: any) => l.completed
            ).length;
            sectionProgress = Math.round(
              (completedLessons / section.lessons.length) * 100
            );
          }
          return (
            <div key={section.id} className="relative pl-8 pb-8">
              {/* Timeline connector (no line after last section) */}
              {!isLastSection && (
                <div className="absolute left-4 top-8 bottom-0 w-0.5">
                  {/* Filled part (progress) */}
                  <div
                    className={`absolute left-0 w-full ${
                      sectionProgress === 100
                        ? "bg-green-500"
                        : sectionProgress > 0
                        ? "bg-primary"
                        : "bg-muted"
                    }`}
                    style={{
                      height: `${sectionProgress}%`,
                      top: 0,
                      borderRadius: 2,
                    }}
                  ></div>
                  {/* Unfilled part */}
                  <div
                    className="absolute left-0 w-full bg-muted"
                    style={{
                      height: `${100 - sectionProgress}%`,
                      bottom: 0,
                      top: `${sectionProgress}%`,
                      borderRadius: 2,
                    }}
                  ></div>
                </div>
              )}
              {/* Milestone marker */}
              <div
                className={`absolute left-4 -translate-x-1/2 w-8 h-8 rounded-full flex items-center justify-center z-10
                  ${
                    isCompleted
                      ? "bg-primary text-white ring-2 ring-primary/20"
                      : isCurrent
                      ? "bg-primary text-white ring-4 ring-primary/20"
                      : "bg-muted border-2 border-primary/30 text-muted-foreground"
                  }`}
              >
                {isCompleted ? (
                  <CheckCircle size={18} className="text-white-600" />
                ) : (
                  <span className="text-sm font-medium">
                    {sectionIndex + 1}
                  </span>
                )}
              </div>
              {/* Section content */}
              <div className="pl-4">
                <div className="font-semibold text-base leading-tight mb-2">
                  {section.title}
                </div>

                {/* Lessons for this section */}
                <div className="space-y-1  ">
                  {section.lessons?.map((lesson, lessonIndex) => {
                    console.log("Lesson object:", lesson); // Debug: log each lesson object
                    const isActive =
                      sectionIndex === currentSectionIndex &&
                      lessonIndex === currentLessonIndex;
                    return (
                      <div
                        key={lesson.id}
                        className={`flex items-center py-2 px-3 rounded-md transition-colors duration-200
                          ${
                            isActive
                              ? "bg-primary/10 border-l-2 border-primary"
                              : ""
                          }
                          ${
                            !isActive
                              ? `bg-gradient-to-r from-[#f3e8ff] via-[#ede9fe] to-[#e0e7ff] dark:from-[#2a204d]/40 dark:via-[#2a204d]/30 dark:to-[#1e1b4b]/30 border-2 border-[#d1c4e9] dark:border-[#2a204d]/60 shadow-sm`
                              : ""
                          }
                          ${
                            lesson.locked
                              ? "opacity-60"
                              : "hover:bg-[#ede9fe]/80 dark:hover:bg-[#2a204d]/50 cursor-pointer"
                          }
                        `}
                        onClick={() => {
                          if (!lesson.locked) {
                            console.log("Lesson videoUrl:", lesson.videoUrl);
                            onSelectLesson(
                              currentModuleIndex,
                              sectionIndex,
                              lessonIndex,
                              lesson.videoUrl || ""
                            );
                          }
                        }}
                      >
                        <div className="mr-3">
                          {(lesson as any).status === "completed" ? (
                            <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center animate-in fade-in-50">
                              <Check size={12} className="text-white" />
                            </div>
                          ) : lesson.locked ? (
                            <Lock size={16} className="text-muted-foreground" />
                          ) : (
                            <div
                              className={`w-5 h-5 rounded-full border ${
                                isActive
                                  ? "border-primary"
                                  : "border-muted-foreground"
                              }`}
                            ></div>
                          )}
                        </div>
                        <div className="flex-grow">
                          <div className="font-medium text-sm">
                            {lesson.title}
                          </div>
                          {lesson.duration &&
                            lesson.duration !== "0" &&
                            lesson.duration !== "0:00" && (
                              <div className="text-xs text-muted-foreground">
                                {lesson.duration} min
                              </div>
                            )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CourseModuleTimeline;
