"use client";

import React, { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  ArrowRight,
  User,
  Clock,
  BookOpen,
  CheckCircle,
  Loader2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import apiService from "@/services/apiService";
import { Course } from "@/types/api";

interface CourseOfferingsProps {
  onSelectTrack: (track: string) => void;
}

const CourseOfferings = ({ onSelectTrack }: CourseOfferingsProps) => {
  const router = useRouter();
  const sectionRef = useRef<HTMLDivElement>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch courses from API
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        setError(null);
        const fetchedCourses = await apiService.getCourses();
        setCourses(fetchedCourses);
      } catch (err) {
        console.error("Error fetching courses:", err);
        setError("Failed to load courses. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  const handleSelectCourse = (id: string | number) => {
    // Update selected track for the roadmap on the homepage
    onSelectTrack(id.toString());

    // Navigate to the course detail page using Next.js router
    router.push(`/course-detail/${id}`);
  };

  // Helper function to get badge text based on course badge
  const getBadgeText = (badge?: string) => {
    switch (badge) {
      case "Most Popular":
        return "Most Popular";
      case "High Demand":
        return "High Demand";
      case "Comprehensive":
        return "Comprehensive";
      default:
        return "New";
    }
  };

  if (loading) {
    return (
      <section className="py-11 px-2 sm:px-6 relative overflow-hidden">
        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold mb-4">
              <span className="bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach bg-clip-text text-transparent">
                Explore Our Courses
              </span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Break into tech or level up your skills with hands-on, expert-led
              courses. Pick your path, build real projects, and join a
              future-focused community.
            </p>
          </div>
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">Loading courses...</span>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-11 px-2 sm:px-6 relative overflow-hidden">
        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold mb-4">
              <span className="bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach bg-clip-text text-transparent">
                Explore Our Courses
              </span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Break into tech or level up your skills with hands-on, expert-led
              courses. Pick your path, build real projects, and join a
              future-focused community.
            </p>
          </div>
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <p className="text-red-500 mb-4">{error}</p>
              <Button 
                onClick={() => window.location.reload()}
                className="bg-gradient-to-r from-upskilleo-purple via-secondary to-upskilleo-deep-purple text-white border-0 shadow-md hover:from-upskilleo-deep-purple hover:to-upskilleo-purple"
              >
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section
      ref={sectionRef}
      className="py-11 px-2 sm:px-6 relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute -top-40 right-10 w-80 h-80 bg-upskilleo-purple/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-40 -left-20 w-72 h-72 bg-upskilleo-purple/10 rounded-full blur-3xl"></div>
      {/* Removed orange/yellow glowing element */}

      {/* Theme glowing element at bottom right, softly peeking from the corner */}
      <div className="pointer-events-none absolute bottom-0 right-0 w-96 h-96 bg-upskilleo-purple/20 rounded-full blur-3xl z-0 translate-x-1/3 translate-y-1/3"></div>

      <div className="container mx-auto max-w-6xl relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold mb-4">
            <span className="bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach bg-clip-text text-transparent">
              Explore Our Courses
            </span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Break into tech or level up your skills with hands-on, expert-led
            courses. Pick your path, build real projects, and join a
            future-focused community.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {courses.map((course) => (
            <Card
              key={course.id}
              className="overflow-hidden border-border hover:border-primary/50 transition-transform duration-300 hover:shadow-lg hover:shadow-primary/5 transform hover:scale-105"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={course.image || "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?auto=format&fit=crop&q=80&w=500&h=300"}
                  alt={course.title}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-background to-transparent"></div>
                <div className="absolute top-4 left-4">
                  <Badge
                    className={`bg-gradient-to-r ${course.color || "from-blue-500 to-indigo-500"} border-none text-white`}
                  >
                    {getBadgeText(course.badge)}
                  </Badge>
                </div>
                <div className="absolute bottom-4 left-4 right-4">
                  <h3 className="text-xl font-bold text-white">
                    {course.title}
                  </h3>
                </div>
              </div>

              <CardContent className="p-6">
                <p className="text-muted-foreground mb-4">
                  {course.description}
                </p>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <BookOpen size={16} className="text-primary" />
                    <div>
                      <p className="text-xs text-muted-foreground">Modules</p>
                      <p className="font-medium">{course.modules.length}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock size={16} className="text-primary" />
                    <div>
                      <p className="text-xs text-muted-foreground">Projects</p>
                      <p className="font-medium">{course.projects || 0}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 col-span-2">
                    <User size={16} className="text-primary" />
                    <div>
                      <p className="text-xs text-muted-foreground">Level</p>
                      <p className="font-medium">{course.level || "Beginner"}</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Key Topics:</p>
                  <ul className="space-y-1">
                    {(course.features || []).map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <CheckCircle
                          size={14}
                          className="text-primary mt-1 flex-shrink-0"
                        />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>

              <CardFooter className="p-6 pt-0">
                <Button
                  className="w-full group bg-gradient-to-r from-upskilleo-purple via-secondary to-upskilleo-deep-purple text-white border-0 shadow-md hover:from-upskilleo-deep-purple hover:to-upskilleo-purple"
                  onClick={() => handleSelectCourse(course.id)}
                >
                  View Details
                  <ArrowRight
                    className="ml-2 group-hover:translate-x-1 transition-transform"
                    size={16}
                  />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CourseOfferings;
