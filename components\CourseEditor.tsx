import React, { useState, useEffect, useCallback } from "react";
import EditorHeader from "@/components/editor/EditorHeader";
import { useFullscreen } from "@/components/useFullscreen";
import HintsSection from "@/components/editor/HintsSection";
import EditorTabs from "@/components/editor/EditorTabs";
import CodeEditor from "@/components/editor/CodeEditor";
import PreviewIframe from "@/components/editor/PreviewIframe";
import ActionButtons from "@/components/editor/ActionButtons";


interface CourseEditorProps {
  code: string;
  onChange: (code: string) => void;
  language: string;
  hints?: string[];
  onSkip?: () => void;
  onSubmit?: () => void;
  onReset?: () => void;
  initialCode?: string;
  initialHtmlCode?: string;
  initialCssCode?: string;
  initialJsCode?: string;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
  onContinueVideo?: () => void; // New prop to continue video after submission
  onBackToVideo?: () => void; // New prop for back to video
}

const CourseEditor: React.FC<CourseEditorProps> = ({
  code,
  onChange,
  language,
  hints = [],
  onSkip,
  onSubmit,
  onReset,
  initialCode = "",
  initialHtmlCode = "",
  initialCssCode = "",
  initialJsCode = "",
  isFullscreen = false,
  onToggleFullscreen,
  onContinueVideo, // Add new prop for continuing video playback
  onBackToVideo, // Add new prop for back to video
}) => {
  // --- Fullscreen container logic ---
  const { elementRef: fullscreenRef } = useFullscreen<HTMLDivElement>();
  const [showHints, setShowHints] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [htmlCode, setHtmlCode] = useState(
    initialHtmlCode ||
      "<div>\n  <!-- Your HTML here -->\n  <h1>Hello World</h1>\n</div>"
  );
  const [cssCode, setCssCode] = useState(
    initialCssCode || "/* Your CSS here */\nh1 {\n  color: blue;\n}"
  );
  const [jsCode, setJsCode] = useState(
    initialJsCode || '// Your JavaScript here\nconsole.log("Hello World");'
  );
  const [activeTab, setActiveTab] = useState("html");
  const [shouldFocus, setShouldFocus] = useState(false);

  // Add state for force refresh
  const [forceRefresh, setForceRefresh] = useState(false);
  
  const [isInitialized, setIsInitialized] = useState(false);

  // Define file tabs
  const fileTabs = [
    { id: "html", name: "index.html", type: "html" },
    { id: "css", name: "index.css", type: "css" },
    { id: "js", name: "index.js", type: "js" },
  ];

  // Initialize code based on language if not already initialized
  useEffect(() => {
    if (!isInitialized) {
      // Set initial values from props if provided
      if (initialHtmlCode) {
        setHtmlCode(initialHtmlCode);
      }
      if (initialCssCode) {
        setCssCode(initialCssCode);
      }
      if (initialJsCode) {
        setJsCode(initialJsCode);
      }

      // Determine which tab should be active based on available content
      if (initialHtmlCode && initialHtmlCode.trim() !== "") {
        setActiveTab("html");
      } else if (initialCssCode && initialCssCode.trim() !== "") {
        setActiveTab("css");
      } else if (initialJsCode && initialJsCode.trim() !== "") {
        setActiveTab("js");
      }

      // If code is already set and no initial file codes provided, determine which tab to update
      if (
        code &&
        code.trim() !== "" &&
        !initialHtmlCode &&
        !initialCssCode &&
        !initialJsCode
      ) {
        if (language === "html" || language === "htm") {
          setHtmlCode(code);
          setActiveTab("html");
        } else if (language === "css") {
          setCssCode(code);
          setActiveTab("css");
        } else if (language === "javascript" || language === "js") {
          setJsCode(code);
          setActiveTab("js");
        }
      }
      setIsInitialized(true);
    }
  }, [
    code,
    language,
    isInitialized,
    initialHtmlCode,
    initialCssCode,
    initialJsCode,
  ]);

  // Memorized function to update the main code to prevent unnecessary re-renders
  const updateParentCode = useCallback(
    (newCode: string) => {
      if (onChange && newCode !== code) {
        onChange(newCode);
      }
    },
    [onChange, code]
  );

  // Update the main code when any of the tab codes change, but avoid unnecessary renders
  useEffect(() => {
    if (!isInitialized) return;

    let newCode = "";
    if (language === "html" || language === "htm") {
      newCode = htmlCode;
    } else if (language === "css") {
      newCode = cssCode;
    } else if (language === "javascript" || language === "js") {
      newCode = jsCode;
    } else {
      // For other languages, use the existing code
      return;
    }

    updateParentCode(newCode);
  }, [htmlCode, cssCode, jsCode, language, isInitialized, updateParentCode]);

  const handleReset = () => {
    if (initialCode) {
      // Reset the appropriate code based on language
      if (language === "html" || language === "htm") {
        setHtmlCode(initialCode);
      } else if (language === "css") {
        setCssCode(initialCode);
      } else if (language === "javascript" || language === "js") {
        setJsCode(initialCode);
      }

      // Also update the parent code
      onChange(initialCode);
    }
    if (onReset) {
      onReset();
    }
  };

  const handleCodeChange = (value: string | undefined, type: string) => {
    const newValue = value || "";
    if (type === "html") {
      setHtmlCode(newValue);
    } else if (type === "css") {
      setCssCode(newValue);
    } else if (type === "js") {
      setJsCode(newValue);
    }
    // Mark code as changed when any edit occurs
    
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    setShouldFocus(true);
    // Reset focus flag after a short delay to allow the editor to mount first
    setTimeout(() => setShouldFocus(false), 100);
  };

  // Handle refresh preview
  const handleRefreshPreview = () => {
    setForceRefresh((prev) => !prev); // Toggle to trigger refresh
    
  };

  // Handle code change notification
  const handleCodeChangeNotification = () => {
    
  };

  // Define the handleTogglePreview function
  const handleTogglePreview = () => {
    setShowPreview(!showPreview);
    // Always refresh when toggling to preview
    if (!showPreview) {
      // Use a small delay to ensure the preview component is mounted
      setTimeout(() => handleRefreshPreview(), 100);
      
    }
  };

  // This function now only refreshes the preview without toggling
  

  // New handler for handling submit and continuing video playback
  

  return (
    <div
      ref={isFullscreen ? fullscreenRef : undefined}
      className={`rounded-md border overflow-hidden shadow-md relative h-full flex flex-col`}
    >
      {/* Remove absolute-positioned fullscreen and back-to-video buttons */}
      <EditorHeader
        language={activeTab}
        hints={hints}
        showHints={showHints}
        showPreview={showPreview}
        onToggleHints={() => setShowHints(!showHints)}
        onTogglePreview={handleTogglePreview}
        onRefreshPreview={handleRefreshPreview}
        editorMode="web"
        isFullscreen={isFullscreen}
        onToggleFullscreen={onToggleFullscreen}
      />

      {/* Hints section */}
      <HintsSection hints={hints} show={showHints} />

      {showPreview ? (
        <div className="flex-1 min-h-0">
          <PreviewIframe
            htmlCode={htmlCode}
            cssCode={cssCode}
            jsCode={jsCode}
            language={activeTab}
            isFullscreen={isFullscreen}
            onToggleFullscreen={onToggleFullscreen}
            forceRefresh={forceRefresh}
          />
        </div>
      ) : (
        <div className="w-full">
          <div className="flex justify-between items-center bg-[#252526]">
            <EditorTabs
              activeTab={activeTab}
              onTabChange={handleTabChange}
              tabs={fileTabs}
            />
          </div>

          <div className="p-0 m-0">
            {activeTab === "html" && (
              <CodeEditor
                language="html"
                code={htmlCode}
                onChange={(value) => handleCodeChange(value, "html")}
                autoFocus={shouldFocus && activeTab === "html"}
                onCodeChange={handleCodeChangeNotification}
                isFullscreen={isFullscreen}
              />
            )}

            {activeTab === "css" && (
              <CodeEditor
                language="css"
                code={cssCode}
                onChange={(value) => handleCodeChange(value, "css")}
                autoFocus={shouldFocus && activeTab === "css"}
                onCodeChange={handleCodeChangeNotification}
                isFullscreen={isFullscreen}
              />
            )}

            {activeTab === "js" && (
              <CodeEditor
                language="javascript"
                code={jsCode}
                onChange={(value) => handleCodeChange(value, "js")}
                autoFocus={shouldFocus && activeTab === "js"}
                onCodeChange={handleCodeChangeNotification}
                isFullscreen={isFullscreen}
              />
            )}
          </div>
        </div>
      )}

      {/* Action buttons */}
      <ActionButtons
        onReset={handleReset}
        onSkip={onSkip}
        onSubmit={onSubmit}
        showPreview={showPreview}
        onTogglePreview={handleTogglePreview}
        onContinueVideo={onContinueVideo}
        onBackToVideo={onBackToVideo}
        fullscreenContainer={isFullscreen ? fullscreenRef.current : undefined}
      />
    </div>
  );
};

export default CourseEditor;
