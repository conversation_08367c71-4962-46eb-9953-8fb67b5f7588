import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Award,
  Briefcase,
  FileText,
  User,
  CheckCircle,
  Lock,
  ExternalLink,
  Linkedin,
  Link,
  Github,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface CareerRoadmapProps {
  courseProgress?: number;
}

const CareerRoadmap: React.FC<CareerRoadmapProps> = ({
  courseProgress = 0,
}) => {
  const [activeSection, setActiveSection] = useState<string | null>(null);

  const isUnlocked = courseProgress >= 100;

  const handleShareCertificate = (platform: string) => {
    toast.success(`Ready to share on ${platform}`, {
      description: "Your certificate has been prepared for sharing.",
    });
  };

  return (
    <div className="w-full bg-upskilleo-soft-purple/30 p-6 rounded-xl border border-upskilleo-purple/20">
      <h3 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-100">
        Your Success Path
      </h3>

      <div className="grid gap-6 md:grid-cols-4">
        {/* Certificate Card */}
        <Dialog>
          <DialogTrigger asChild>
            <Card className="relative overflow-hidden cursor-pointer group hover:shadow-md hover:scale-[1.02] transition-all duration-200 bg-white dark:bg-gray-800 border-2 border-blue-200 dark:border-blue-900">
              <div className="absolute top-0 left-0 right-0 h-1 bg-blue-500" />
              <CardContent className="p-5">
                <div className="flex flex-col items-center text-center">
                  <div className="w-14 h-14 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-3 group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
                    <Award className="h-7 w-7 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="font-semibold text-lg mb-1 text-gray-800 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    Course Certificate
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Available after completion
                  </p>
                </div>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Course Certificate</DialogTitle>
              <DialogDescription>
                {isUnlocked
                  ? "Congratulations! You've earned your certificate."
                  : "Complete the course to unlock your certificate."}
              </DialogDescription>
            </DialogHeader>
            <div className="relative overflow-hidden rounded-lg border">
              <div className={`p-6 ${isUnlocked ? "" : "blur-sm"}`}>
                <div className="border-8 border-double border-blue-200 p-6 bg-gradient-to-r from-blue-50 to-indigo-50">
                  <div className="flex justify-center mb-4">
                    <Award className="h-12 w-12 text-blue-600" />
                  </div>
                  <h4 className="text-xl font-serif text-center mb-1">
                    Certificate of Completion
                  </h4>
                  <p className="text-center text-sm mb-4">
                    This certifies that
                  </p>
                  <p className="text-center font-semibold text-lg mb-4">
                    John Doe
                  </p>
                  <p className="text-center text-sm mb-4">
                    has successfully completed the course
                  </p>
                  <p className="text-center font-semibold mb-6">
                    Front-end Web Development
                  </p>
                  <div className="flex justify-between items-center">
                    <div className="text-sm">
                      <p>Date: May 9, 2025</p>
                    </div>
                    <div className="text-sm">
                      <p>ID: CERT-FE-12345</p>
                    </div>
                  </div>
                </div>
              </div>

              {!isUnlocked && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 backdrop-blur-[2px]">
                  <div className="bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg">
                    <Lock className="h-8 w-8 text-gray-500" />
                  </div>
                </div>
              )}
            </div>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <div className="relative">
                    <Progress
                      value={courseProgress}
                      className="w-24 h-2 bg-secondary/30"
                    />
                    <div className="absolute inset-0 overflow-hidden pointer-events-none">
                      <div className="absolute top-0 left-0 w-1/2 h-full bg-white/10 transform -skew-x-12 opacity-50 animate-pulse"></div>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {courseProgress}% Complete
                  </p>
                </div>
                {isUnlocked ? (
                  <Button>Download Certificate</Button>
                ) : (
                  <Button variant="outline">Continue Learning</Button>
                )}
              </div>

              {isUnlocked && (
                <div className="border-t pt-4">
                  <p className="text-sm mb-3 text-gray-600 dark:text-gray-300">
                    Share your achievement:
                  </p>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2 bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                      onClick={() => handleShareCertificate("LinkedIn")}
                    >
                      <Linkedin className="h-4 w-4" />
                      LinkedIn
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => handleShareCertificate("Twitter")}
                    >
                      <Link className="h-4 w-4" />
                      Other
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Internship Card */}
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Card className="relative overflow-hidden cursor-pointer group hover:shadow-md hover:scale-[1.02] transition-all duration-200 bg-white dark:bg-gray-800 border-2 border-amber-200 dark:border-amber-900">
              <div className="absolute top-0 left-0 right-0 h-1 bg-amber-500" />
              <CardContent className="p-5">
                <div className="flex flex-col items-center text-center">
                  <div className="w-14 h-14 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center mb-3 group-hover:bg-amber-200 dark:group-hover:bg-amber-800 transition-colors">
                    <Briefcase className="h-7 w-7 text-amber-600 dark:text-amber-400" />
                  </div>
                  <h3 className="font-semibold text-lg mb-1 text-gray-800 dark:text-gray-100 group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors">
                    Internship Opportunity
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    For top performers
                  </p>
                </div>
              </CardContent>
            </Card>
          </AlertDialogTrigger>
          <AlertDialogContent className="max-w-md">
            <AlertDialogHeader>
              <AlertDialogTitle>Internship Opportunities</AlertDialogTitle>
              <AlertDialogDescription>
                As a top performer in this course, you'll gain exclusive access
                to our internship network with leading companies.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="py-4">
              <div className="space-y-3">
                <div className="bg-white dark:bg-gray-800 border border-amber-200 dark:border-amber-900 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 mr-3">
                        <div className="w-10 h-10 bg-amber-100 dark:bg-amber-900 rounded-md flex items-center justify-center">
                          <Briefcase className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-800 dark:text-gray-200">
                          Front-end Developer Intern
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          TechCorp Inc. • Remote • 3 months
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={!isUnlocked}
                      className={!isUnlocked ? "opacity-60" : ""}
                      aria-label="Apply for front-end developer internship"
                    >
                      <span className="mr-1">Apply</span>
                      <ExternalLink className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                </div>
                <div className="bg-white dark:bg-gray-800 border border-amber-200 dark:border-amber-900 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 mr-3">
                        <div className="w-10 h-10 bg-amber-100 dark:bg-amber-900 rounded-md flex items-center justify-center">
                          <Briefcase className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-800 dark:text-gray-200">
                          React Developer
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          StartupXYZ • Hybrid • 6 months
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={!isUnlocked}
                      className={!isUnlocked ? "opacity-60" : ""}
                      aria-label="Apply for react developer position"
                    >
                      <span className="mr-1">Apply</span>
                      <ExternalLink className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                </div>
              </div>

              {!isUnlocked && (
                <div className="mt-4 flex flex-col items-center">
                  <div className="relative w-full">
                    <Progress
                      value={courseProgress}
                      className="h-2 bg-secondary/30"
                    />
                    <div className="absolute inset-0 overflow-hidden pointer-events-none rounded-full">
                      <div className="absolute top-0 left-0 w-1/2 h-full bg-white/10 transform -skew-x-12 opacity-50 animate-pulse"></div>
                    </div>
                  </div>
                  <p className="text-sm text-primary mt-2">
                    Complete the course to unlock application for these
                    opportunities.
                  </p>
                </div>
              )}
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Close</AlertDialogCancel>
              <AlertDialogAction>View All Opportunities</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Career Placement Card */}
        <Dialog>
          <DialogTrigger asChild>
            <Card className="relative overflow-hidden cursor-pointer group hover:shadow-md hover:scale-[1.02] transition-all duration-200 bg-white dark:bg-gray-800 border-2 border-green-200 dark:border-green-900">
              <div className="absolute top-0 left-0 right-0 h-1 bg-green-500" />
              <CardContent className="p-5">
                <div className="flex flex-col items-center text-center">
                  <div className="w-14 h-14 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-3 group-hover:bg-green-200 dark:group-hover:bg-green-800 transition-colors">
                    <User className="h-7 w-7 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="font-semibold text-lg mb-1 text-gray-800 dark:text-gray-100 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                    Career Placement
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Mock Interview Support
                  </p>
                </div>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Mock Interview Support</DialogTitle>
              <DialogDescription>
                {isUnlocked
                  ? "Schedule a mock interview or career advisory call with our experts."
                  : "Complete the course to unlock mock interviews and career guidance."}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="bg-white dark:bg-gray-800 border rounded-lg p-4">
                <h3 className="font-medium text-lg mb-3">Mock Interviews</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 border border-green-100 dark:border-green-900 rounded-md bg-green-50/50 dark:bg-green-950/20">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <p className="font-medium">Technical Interview</p>
                        <p className="text-xs text-muted-foreground">
                          45-60 minutes
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      disabled={!isUnlocked}
                      className={!isUnlocked ? "opacity-60" : ""}
                    >
                      Schedule
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-3 border border-green-100 dark:border-green-900 rounded-md bg-green-50/50 dark:bg-green-950/20">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <p className="font-medium">Behavioral Interview</p>
                        <p className="text-xs text-muted-foreground">
                          30-45 minutes
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      disabled={!isUnlocked}
                      className={!isUnlocked ? "opacity-60" : ""}
                    >
                      Schedule
                    </Button>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 border rounded-lg p-4">
                <h3 className="font-medium text-lg mb-3">
                  Career Expert Calls
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 border border-blue-100 dark:border-blue-900 rounded-md bg-blue-50/50 dark:bg-blue-950/20">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="font-medium">Resume Review</p>
                        <p className="text-xs text-muted-foreground">
                          30 minutes
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      disabled={!isUnlocked}
                      className={!isUnlocked ? "opacity-60" : ""}
                    >
                      Book Call
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-3 border border-blue-100 dark:border-blue-900 rounded-md bg-blue-50/50 dark:bg-blue-950/20">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="font-medium">Career Strategy</p>
                        <p className="text-xs text-muted-foreground">
                          45 minutes
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      disabled={!isUnlocked}
                      className={!isUnlocked ? "opacity-60" : ""}
                    >
                      Book Call
                    </Button>
                  </div>
                </div>
              </div>

              {!isUnlocked && (
                <div className="flex flex-col items-center py-2">
                  <div className="relative w-full">
                    <Progress
                      value={courseProgress}
                      className="h-2 bg-secondary/30"
                    />
                    <div className="absolute inset-0 overflow-hidden pointer-events-none rounded-full">
                      <div className="absolute top-0 left-0 w-1/2 h-full bg-white/10 transform -skew-x-12 opacity-50 animate-pulse"></div>
                    </div>
                  </div>
                  <p className="text-sm text-primary mt-2">
                    Complete the course to unlock these resources
                  </p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline">Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Portfolio Card */}
        <Dialog>
          <DialogTrigger asChild>
            <Card className="relative overflow-hidden cursor-pointer group hover:shadow-md hover:scale-[1.02] transition-all duration-200 bg-white dark:bg-gray-800 border-2 border-purple-200 dark:border-purple-900">
              <div className="absolute top-0 left-0 right-0 h-1 bg-purple-500" />
              <CardContent className="p-5">
                <div className="flex flex-col items-center text-center">
                  <div className="w-14 h-14 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-3 group-hover:bg-purple-200 dark:group-hover:bg-purple-800 transition-colors">
                    <FileText className="h-7 w-7 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h3 className="font-semibold text-lg mb-1 text-gray-800 dark:text-gray-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                    UpskillEO Portfolio
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Showcase your projects
                  </p>
                </div>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent className="sm:max-w-xl">
            <DialogHeader>
              <DialogTitle>Your UpskillEO Portfolio</DialogTitle>
              <DialogDescription>
                Preview of how your portfolio will look after completing this
                course.
              </DialogDescription>
            </DialogHeader>
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-gray-900 text-white p-2 text-xs flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="ml-2">portfolio.upskilleo.dev/johndoe</span>
              </div>
              <div className="p-4 bg-white dark:bg-gray-800">
                <div className="flex items-start mb-6">
                  <div className="w-20 h-20 rounded-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center overflow-hidden border-4 border-white dark:border-gray-700 shadow-md">
                    <span className="text-2xl font-bold text-white">JD</span>
                  </div>
                  <div className="ml-4">
                    <h3 className="font-bold text-xl text-gray-900 dark:text-gray-100">
                      John Doe
                    </h3>
                    <p className="text-sm text-purple-600 dark:text-purple-400 font-medium">
                      Frontend Developer
                    </p>
                    <div className="flex gap-2 mt-2">
                      <Badge
                        variant="outline"
                        className="text-xs border-blue-200 text-blue-700 bg-blue-50 dark:border-blue-900 dark:text-blue-300 dark:bg-blue-900/20"
                      >
                        HTML
                      </Badge>
                      <Badge
                        variant="outline"
                        className="text-xs border-blue-200 text-blue-700 bg-blue-50 dark:border-blue-900 dark:text-blue-300 dark:bg-blue-900/20"
                      >
                        CSS
                      </Badge>
                      <Badge
                        variant="outline"
                        className="text-xs border-blue-200 text-blue-700 bg-blue-50 dark:border-blue-900 dark:text-blue-300 dark:bg-blue-900/20"
                      >
                        JavaScript
                      </Badge>
                      <Badge
                        variant="outline"
                        className="text-xs border-blue-200 text-blue-700 bg-blue-50 dark:border-blue-900 dark:text-blue-300 dark:bg-blue-900/20"
                      >
                        React
                      </Badge>
                    </div>
                    <div className="flex items-center gap-3 mt-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="rounded-full h-8 w-8 bg-gray-100 dark:bg-gray-700"
                      >
                        <Github className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="rounded-full h-8 w-8 bg-gray-100 dark:bg-gray-700"
                      >
                        <Linkedin className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="font-semibold mb-3 text-gray-800 dark:text-gray-200 flex items-center">
                    <Award className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />{" "}
                    Certifications
                  </h4>
                  <div className="border border-gray-200 dark:border-gray-700 rounded-md p-3 mb-2 flex items-center bg-white dark:bg-gray-800">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg mr-3">
                      <Award className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-800 dark:text-gray-200">
                        Front-end Web Development
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        UpskillEO • Issued May 2025
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" className="ml-auto">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-gray-800 dark:text-gray-200 flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-purple-600 dark:text-purple-400" />{" "}
                    Projects
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden bg-white dark:bg-gray-800 hover:shadow-md transition-all duration-200">
                      <div className="h-32 bg-gradient-to-br from-blue-500/20 to-purple-500/20 relative overflow-hidden">
                        <img
                          src="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b"
                          alt="Project thumbnail"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end">
                          <div className="p-2 w-full">
                            <p className="text-white font-medium text-sm">
                              Personal Portfolio
                            </p>
                            <p className="text-white/80 text-xs">
                              React, Tailwind CSS
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="p-3 flex justify-between items-center">
                        <div className="flex">
                          <Badge variant="secondary" className="text-xs">
                            Frontend
                          </Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 w-7 p-0"
                          asChild
                        >
                          <a href="#view-project">
                            <ExternalLink className="h-3.5 w-3.5" />
                          </a>
                        </Button>
                      </div>
                    </div>
                    <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden bg-white dark:bg-gray-800 hover:shadow-md transition-all duration-200">
                      <div className="h-32 bg-gradient-to-br from-green-500/20 to-blue-500/20 relative overflow-hidden">
                        <img
                          src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158"
                          alt="Project thumbnail"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end">
                          <div className="p-2 w-full">
                            <p className="text-white font-medium text-sm">
                              E-commerce Dashboard
                            </p>
                            <p className="text-white/80 text-xs">
                              React, Chart.js
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="p-3 flex justify-between items-center">
                        <div className="flex">
                          <Badge variant="secondary" className="text-xs">
                            Dashboard
                          </Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 w-7 p-0"
                          asChild
                        >
                          <a href="#view-project">
                            <ExternalLink className="h-3.5 w-3.5" />
                          </a>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {!isUnlocked && (
              <div className="mt-4 flex flex-col items-center">
                <div className="relative w-full">
                  <Progress
                    value={courseProgress}
                    className="h-2 bg-secondary/30"
                  />
                  <div className="absolute inset-0 overflow-hidden pointer-events-none rounded-full">
                    <div className="absolute top-0 left-0 w-1/2 h-full bg-white/10 transform -skew-x-12 opacity-50 animate-pulse"></div>
                  </div>
                </div>
                <p className="text-sm text-primary mt-2">
                  Complete the course to unlock your professional portfolio
                </p>
              </div>
            )}

            <DialogFooter>
              <Button variant="outline">Close Preview</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default CareerRoadmap;
