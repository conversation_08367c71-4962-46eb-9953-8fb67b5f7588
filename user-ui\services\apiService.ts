/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import moment from "moment";
import {
  LoginResponse,
  Course,
  CourseDetails,
  ModuleDetails,
} from "@/types/api";

class ApiService {
  private axiosInstance: AxiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8080",
      headers: {
        "Content-Type": "application/json",
        "x-client-time": this.getClientDate(),
      },
    });

    this.setupInterceptors();
  }

  /**
   * Returns the latest token from localStorage (browser-safe).
   */
  private getToken(): string | null {
    return typeof window !== "undefined" ? localStorage.getItem("token") : null;
  }

  /**
   * Returns current formatted client date.
   */
  private getClientDate(): string {
    return moment().format("MM-DD-YYYY");
  }

  /**
   * Sets up request and response interceptors.
   */
  private setupInterceptors(): void {
    this.axiosInstance.interceptors.request.use((config: any) => {
      const token = this.getToken();
      config.headers["x-client-time"] = this.getClientDate();

      if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
      }

      return config;
    });

    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        const { status, data } = error.response || {};
        const msg = data?.errors?.[0]?.msg;

        if (
          status === 401 &&
          ["No token provided", "Invalid token", "Invalid token."].includes(msg)
        ) {
          console.warn("Token error:", msg);
          this.handleTokenError();
        }

        if (
          msg === "Client date is more than 1 day different from server date"
        ) {
          console.warn("Date mismatch error detected");
          this.handleDateMismatch();
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Stores the token in localStorage for future use.
   */
  public setToken(token: string): void {
    if (typeof window !== "undefined") {
      localStorage.setItem("token", token);
    }
  }

  /**
   * Clears token, user data and redirects to login.
   */
  public clearToken(): void {
    if (typeof window !== "undefined") {
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      window.location.href = "/auth";
    }
  }

  /**
   * Handles token errors by logging out.
   */
  private handleTokenError(): void {
    this.clearToken();
  }

  /**
   * Handles date mismatch by logging out and redirecting.
   */
  private handleDateMismatch(): void {
    if (typeof window !== "undefined") {
      sessionStorage.setItem("dateMismatchError", "true");
      this.clearToken();
      window.location.href = "/auth";
    }
  }

  /**
   * Generic API call method using Axios.
   */
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.request(
        config
      );
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.error || "API request failed.");
      } else if (error.request) {
        throw new Error("No response from the API.");
      } else {
        throw new Error(error.message || "Unexpected error occurred.");
      }
    }
  }

  public get<T>(url: string, params?: any): Promise<T> {
    return this.request({ method: "GET", url, params });
  }

  public post<T>(url: string, data: any): Promise<T> {
    return this.request({ method: "POST", url, data });
  }

  public put<T>(url: string, data: any): Promise<T> {
    return this.request({ method: "PUT", url, data });
  }

  public delete<T>(url: string, params?: any): Promise<T> {
    return this.request({ method: "DELETE", url, params });
  }

  // Learning Platform Specific API Methods

  /**
   * Login user with email and password
   */
  public async login(email: string, password: string): Promise<LoginResponse> {
    const response = await this.post<LoginResponse>("/login", {
      email,
      password,
    });
    if (response.accessToken) {
      this.setToken(response.accessToken);
    }
    // Store user data in localStorage
    if (response.user && typeof window !== "undefined") {
      localStorage.setItem("user", JSON.stringify(response.user));
    }
    return response;
  }

  /**
   * Get all available courses
   */
  public async getCourses(): Promise<Course[]> {
    return this.get<Course[]>("/courses");
  }

  /**
   * Enroll user in a course
   */
  public async enrollInCourse(
    courseId: string | number
  ): Promise<{ message: string }> {
    return this.post<{ message: string }>(`/enroll/${courseId}`, {});
  }

  /**
   * Get detailed course information with user progress
   */
  public async getCourseDetails(courseId: string): Promise<CourseDetails> {
    return this.get<CourseDetails>(`/course-details/${courseId}`);
  }

  /**
   * Get detailed module information with lessons and progress
   */
  public async getModuleDetails(moduleId: string): Promise<ModuleDetails> {
    return this.get<ModuleDetails>(`/module-details/${moduleId}`);
  }

  /**
   * Update lesson progress
   */
  public async updateLessonProgress(
    moduleId: string,
    lessonId: string,
    duration: number
  ): Promise<{ marked: boolean }> {
    return this.post<{ marked: boolean }>(
      `/lesson/progress/${moduleId}/${lessonId}`,
      {
        duration,
      }
    );
  }

  /**
   * Update breakpoint completion status
   */
  public async updateBreakpoint(
    moduleId: string,
    lessonId: string,
    breakpointId: string,
    completed: boolean,
    editorSettings: Array<{ type: string; userCode: string }>
  ): Promise<{ marked: boolean }> {
    return this.post<{ marked: boolean }>(
      `/lesson/breakpoint/${moduleId}/${lessonId}/${breakpointId}`,
      {
        completed,
        editorSettings,
      }
    );
  }

  /**
   * Makes a streaming POST request to the endpoint.
   */
  public async stream<T>(
    endpoint: string,
    data: any,
    onData: (chunk: T) => void
  ): Promise<void> {
    try {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8080"
        }${endpoint}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.getToken()}`,
            "x-client-time": this.getClientDate(),
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        throw new Error(
          `Streaming failed: ${response.status} ${response.statusText}`
        );
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let buffer = "";

      while (reader) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const payload = line.substring(6);
            if (payload === "[DONE]") return;

            try {
              const parsed = JSON.parse(payload) as T;
              onData(parsed);
            } catch (err) {
              console.error("Error parsing stream chunk:", err, payload);
            }
          }
        }
      }
    } catch (error) {
      console.error("Streaming error:", error);
      throw error;
    }
  }

  /**
   * Optionally get headers manually (for fetch etc.).
   */
  public getAuthHeaders(): Record<string, string> {
    const token = this.getToken();
    return {
      "Content-Type": "application/json",
      "x-client-time": this.getClientDate(),
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    };
  }

  // New method for fetching course enrollment data (Learning Roadmap section)
  async getCourseEnrollmentData(courseId: string): Promise<any> {
    try {
      const response = await this.axiosInstance.get(
        `/enroll/course/${courseId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching course enrollment data:", error);
      throw error;
    }
  }

  /**
   * Get user enrollment information
   */
  public async getUserEnrollments(): Promise<{
    enrolledCoursesCount: number;
    enrolledCourses: Array<{
      id: string;
      slug: string;
      title: string;
      description: string;
      image: string;
      progress: number;
      completedLessons: number;
      enrolledAt: string;
    }>;
  }> {
    return this.get("/user/enrollments");
  }

  /**
   * Get user details and enrollment status for a specific course
   */
  public async getUserDetails(courseId?: string): Promise<{
    user: {
      id: string;
      email: string;
      name: string;
      enrolledCoursesCount: number;
      enrolledCourseIds: string[];
    };
    courseEnrollment?: {
      isEnrolled: boolean;
      courseDetails?: {
        id: string;
        slug: string;
        title: string;
        description: string;
        image: string;
        progress: number;
        completedLessons: number;
        enrolledAt: string;
      };
    };
  }> {
    if (courseId) {
      return this.get(`/user/details/${courseId}`);
    } else {
      return this.get("/user/details");
    }
  }

  /**
   * Get comprehensive enrolled courses data for learning dashboard
   */
  public async getEnrolledCourses(): Promise<{
    success: boolean;
    data: {
      user: {
        id: string;
        email: string;
        name: string;
        enrolledCoursesCount: number;
      };
      enrolledCourses: Array<{
        id: string;
        slug: string;
        title: string;
        description: string;
        longDescription: string;
        image: string;
        level: string;
        instructor: string;
        rating: number;
        reviews: number;
        students: number;
        lastUpdated: string;
        price: number;
        progress: number;
        completedLessons: number;
        totalLessons: number;
        enrolledAt: string;
        lastAccessed: string;
        nextLesson: string;
        duration: string;
        lessons: number;
        modules: number;
        isCompleted: boolean;
        isInProgress: boolean;
        isNotStarted: boolean;
        features: string[];
        topics: string[];
        badge?: string;
        color?: string;
      }>;
      statistics: {
        totalCourses: number;
        completedCourses: number;
        inProgressCourses: number;
        averageProgress: number;
        totalMinutesLearned: number;
      };
    };
  }> {
    return this.get("/user/enrolled-courses");
  }

  // Dynamic enrollment management methods

  /**
   * Add sample enrollments for testing
   */
  public async addSampleEnrollments(): Promise<{
    message: string;
    enrolledCourses: string[];
  }> {
    return this.post("/admin/add-sample-enrollments", {});
  }

  /**
   * Update course progress
   */
  public async updateCourseProgress(
    courseId: string,
    progress: number,
    completedLessons: number
  ): Promise<{
    message: string;
    courseId: string;
    progress: number;
    completedLessons: number;
  }> {
    return this.put(`/enrollment/${courseId}/progress`, {
      progress,
      completedLessons,
    });
  }

  /**
   * Remove course enrollment
   */
  public async removeEnrollment(courseId: string): Promise<{
    message: string;
    courseId: string;
  }> {
    return this.delete(`/enrollment/${courseId}`);
  }

  /**
   * Get all enrollments (admin only)
   */
  public async getAllEnrollments(): Promise<{
    userTracking: Record<string, any>;
    totalUsers: number;
  }> {
    return this.get("/admin/enrollments");
  }

  /**
   * Clear all enrollments for a user (admin only)
   */
  public async clearUserEnrollments(userEmail: string): Promise<{
    message: string;
    userEmail: string;
  }> {
    return this.delete(`/admin/clear-enrollments/${userEmail}`);
  }

  /**
   * Get public course details (no authentication required)
   */
  public async getPublicCourseDetails(courseId: string): Promise<any> {
    const response = await axios.get(
      `${
        process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8080"
      }/public/course-details/${courseId}`
    );
    return response.data;
  }

  /**
   * Check if user is enrolled in a course (lightweight)
   */
  public async getEnrollmentStatus(courseId: string): Promise<boolean> {
    const response = await this.get<{ isEnrolled: boolean }>(
      `/enrollment-status/${courseId}`
    );
    return response.isEnrolled;
  }
}

// Singleton export
const apiService = new ApiService();
export default apiService;
