/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @next/next/no-assign-module-variable */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import React, { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import CourseSectionDetail from "@/components/CourseSectionDetail";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { BookOpen, ChevronRight, Clock, Award, BarChart } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Course, CourseModule, CourseSection } from "@/types/course";
import apiService from "@/services/apiService";

const ModuleDetail = () => {
  const params = useParams();
  const router = useRouter();
  const [moduleData, setModuleData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    async function fetchModule() {
      try {
        const moduleId = Array.isArray(params.moduleId)
          ? params.moduleId[0]
          : params.moduleId;
        const data = await apiService.getModuleDetails(moduleId);
        console.log("mm-data", data);
        setModuleData(data);
        toast.success(`Started ${data.title || "Module"}`, {
          description:
            "Interactive video and coding challenges are ready for you",
          position: "bottom-right",
        });
      } catch (e: any) {
        setError("Module not found");
        toast.error("Module not found", {
          description: "Redirecting to course list",
          position: "bottom-right",
        });
        setTimeout(() => {
          router.push("/");
        }, 2000);
      } finally {
        setLoading(false);
      }
    }
    if (params.moduleId) fetchModule();
  }, [params.moduleId, router]);

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-muted/30 relative">
          {/* Background elements */}
          <div className="fixed inset-0 z-0 pointer-events-none">
            <div className="absolute -top-20 -right-40 w-[40rem] h-[40rem] bg-upskilleo-purple/10 rounded-full blur-3xl transform-gpu"></div>
            <div className="absolute top-1/3 -left-60 w-[35rem] h-[35rem] bg-blue-500/10 rounded-full blur-3xl transform-gpu"></div>
            <div className="absolute bottom-0 right-0 w-[30rem] h-[30rem] bg-secondary/10 rounded-full blur-3xl transform-gpu"></div>
          </div>
          <div className="container mx-auto py-16 px-4 sm:px-6 relative z-10">
            <div className="max-w-6xl mx-auto">
              {/* Skeleton Loading UI */}
              <div className="h-64 mb-12 rounded-xl bg-card/50 animate-pulse relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-b from-transparent to-background/80"></div>
                <div className="absolute bottom-8 left-8 space-y-4 w-2/3">
                  <Skeleton className="h-10 w-3/4 bg-muted/50" />
                  <Skeleton className="h-5 w-1/2 bg-muted/30" />
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-24 rounded-full bg-muted/20" />
                    <Skeleton className="h-6 w-24 rounded-full bg-muted/20" />
                  </div>
                </div>
              </div>
              <div className="grid md:grid-cols-12 gap-6">
                <div className="md:col-span-4 lg:col-span-3">
                  <Skeleton className="h-[calc(100vh-300px)] rounded-lg bg-card/50" />
                </div>
                <div className="md:col-span-8 lg:col-span-9">
                  <Skeleton className="h-96 rounded-lg bg-card/50" />
                  <div className="mt-4 space-y-3">
                    <Skeleton className="h-4 w-full bg-muted/30" />
                    <Skeleton className="h-4 w-5/6 bg-muted/30" />
                    <Skeleton className="h-4 w-4/6 bg-muted/30" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  if (error || !moduleData) {
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-muted/30 relative">
          {/* Background elements */}
          <div className="fixed inset-0 z-0 pointer-events-none">
            <div className="absolute -top-20 -right-40 w-[40rem] h-[40rem] bg-upskilleo-purple/10 rounded-full blur-3xl transform-gpu"></div>
            <div className="absolute top-1/3 -left-60 w-[35rem] h-[35rem] bg-blue-500/10 rounded-full blur-3xl transform-gpu"></div>
          </div>
          <div className="container mx-auto py-16 px-4 relative z-10 flex flex-col items-center justify-center">
            <Card className="max-w-md w-full bg-card/95 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <h1 className="text-2xl font-bold mb-3">Module not found</h1>
                <p className="text-muted-foreground mb-6">
                  The requested module does not exist or could not be loaded.
                </p>
                <div className="flex justify-center">
                  <button
                    onClick={() => router.push("/")}
                    className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                  >
                    <ChevronRight className="mr-2 h-4 w-4" /> Return to Home
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Navbar />
      <CourseSectionDetail
        title={moduleData?.title || ""}
        description={moduleData?.description || ""}
        modules={[
          {
            id: moduleData.id,
            title: moduleData.title,
            description: moduleData.description,
            duration: "",
            lessons: [],
            totalLessons:
              moduleData.sections?.reduce(
                (acc: number, s: any) => acc + (s.lessons?.length || 0),
                0
              ) || 0,
            completed: false,
            locked: false,
            progress: 0,
            sections:
              moduleData.sections?.map((section: any) => ({
                id: section.id,
                title: section.title,
                duration:
                  section.lessons
                    ?.reduce(
                      (acc: number, l: any) =>
                        acc + (parseInt(l.totalDuration) || 0),
                      0
                    )
                    .toString() || "0",
                completed: section.status === "completed",
                locked: section.locked,
                videoUrl: section.lessons?.[0]?.videoUrl || "",
                description: section.description,
                lessons:
                  section.lessons?.map((lesson: any) => ({
                    id: lesson.id,
                    title: lesson.title || "",
                    description: lesson.description,
                    duration: lesson.totalDuration,
                    videoUrl: lesson.videoUrl,
                    breakpoints: lesson.breakpoints,
                    locked: lesson.locked,
                    status: lesson.status,
                  })) || [],
              })) || [],
          },
        ]}
        courseName={moduleData?.courseName}
        moduleNumber={moduleData?.moduleNumber}
        courseId={moduleData?.courseId}
      />
      <Footer />
    </>
  );
};

export default ModuleDetail;
