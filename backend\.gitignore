# Node modules
node_modules/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.*.local

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Grunt intermediate storage
.grunt/

# Bower dependency directory
bower_components/

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# Optional npm cache directory
.npm/

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Mac system files
.DS_Store

# Output of `npm pack`
*.tgz

# Build directory (if you're using a build process)
dist/
build/

# PM2 logs and process files
pids
*.pid
*.log
pm2.log
