"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Card,
  CardHeader,
  CardContent,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Clock,
  CheckCircle,
  ChevronRight,
  Play,
  BookOpen,
  ArrowRight,
  ChevronDown,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { CourseModule } from "@/types/course";
import ModuleSection from "./ModuleSection";
import ModuleProgressBar from "./ModuleProgressBar";

interface ModuleCardProps {
  module: CourseModule;
  index: number;
  isExpanded: boolean;
  toggleModuleExpansion: (moduleId: string) => void;
}

const ModuleCard: React.FC<ModuleCardProps> = ({
  module,
  index,
  isExpanded,
  toggleModuleExpansion,
}) => {
  const router = useRouter();

  const handleStartModule = () => {
    router.push(`/module/${module.id}`);
  };

  return (
    <Card
      className={`overflow-hidden transition-all duration-300 hover:shadow-md ${
        isExpanded ? "ring-2 ring-primary/30 shadow-lg" : ""
      }`}
    >
      <Collapsible
        open={isExpanded}
        onOpenChange={() => toggleModuleExpansion(module.id)}
      >
        <CardHeader className="p-4 bg-muted/30">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3 flex-1">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  module.completed
                    ? "bg-green-500/20 text-green-500"
                    : module.progress > 0
                    ? "bg-amber-500/20 text-amber-500"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                {module.completed ? (
                  <CheckCircle size={16} />
                ) : (
                  <span className="text-sm font-semibold">{index + 1}</span>
                )}
              </div>
              <div>
                <CardTitle className="text-base sm:text-lg font-semibold group-hover:text-primary transition-colors">
                  {module.title}
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {module.description}
                </p>
              </div>
            </div>
            <div className="flex flex-col items-end gap-2 ml-4">
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground font-medium">
                  {module.progress}% Complete
                </span>
                <Progress value={module.progress} className="w-20 h-1.5" />
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">
                  <Clock className="inline h-3 w-3 mr-1" />
                  {module.duration}
                </span>
                <span className="text-xs text-muted-foreground">
                  <BookOpen className="inline h-3 w-3 mr-1" />
                  {module.sections?.length || 0} lessons
                </span>
              </div>
            </div>
          </div>

          {/* New prominent expansion CTA */}
          <div className="mt-3 flex items-center justify-between pt-3 border-t border-border/50">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className={`group transition-all duration-200 ${
                  isExpanded
                    ? "bg-primary/10 text-primary border-primary/30"
                    : "hover:bg-muted/80"
                }`}
                onClick={() => toggleModuleExpansion(module.id)}
              >
                <BookOpen
                  className={`h-4 w-4 mr-2 transition-transform duration-200 ${
                    isExpanded ? "rotate-12" : ""
                  }`}
                />
                {isExpanded ? "Hide Lessons" : "View Lessons"}
                <ChevronDown
                  className={`h-4 w-4 ml-1 transition-transform duration-200 ${
                    isExpanded ? "rotate-180" : ""
                  }`}
                />
              </Button>
              {!isExpanded && module.sections && module.sections.length > 0 && (
                <span className="text-xs text-muted-foreground">
                  {module.sections.filter((s) => s.completed).length}/
                  {module.sections.length} completed
                </span>
              )}
            </div>
            <Button
              size="sm"
              variant={module.completed ? "outline" : "default"}
              className={
                module.completed
                  ? "bg-green-500/5 text-green-600 border-green-200 hover:bg-green-500/10"
                  : "bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white shadow-md hover:scale-105 border-none"
              }
              onClick={handleStartModule}
            >
              {module.completed ? (
                <>
                  <ArrowRight size={16} className="mr-1" />
                  Review Module
                </>
              ) : module.progress > 0 ? (
                <>
                  <Play size={16} className="mr-1" />
                  Continue Learning
                </>
              ) : (
                <>
                  <Play size={16} className="mr-1" />
                  Start Learning
                </>
              )}
            </Button>
          </div>
        </CardHeader>

        <CollapsibleContent className="animate-in fade-in-50 slide-in-from-top-2 duration-200">
          <CardContent className="p-4 pt-0 border-t-0">
            {/* Module metadata */}
            <div className="flex flex-wrap gap-2 my-3">
              {module.badge && (
                <Badge variant="outline" className="bg-primary/5">
                  {module.badge}
                </Badge>
              )}
              {module.track && (
                <Badge variant="outline" className="bg-secondary/5">
                  Track: {module.track}
                </Badge>
              )}
              {module.topics &&
                module.topics.map((topic) => (
                  <Badge key={topic} variant="outline" className="bg-muted">
                    {topic}
                  </Badge>
                ))}
            </div>

            {/* Progress visualization */}
            <ModuleProgressBar progress={module.progress} />

            <div className="mt-4 space-y-2">
              {module.sections &&
                module.sections.map((section, sectionIndex) => (
                  <ModuleSection
                    key={section.id}
                    moduleId={module.id}
                    section={section}
                    sectionIndex={sectionIndex}
                  />
                ))}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export default ModuleCard;
