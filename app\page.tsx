'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Navbar from '@/components/Navbar';
import Hero from '@/components/Hero';
import HowItWorks from '@/components/HowItWorks';
import Footer from '@/components/Footer';
import CourseOfferings from '@/components/CourseOfferings';
import LearningDashboard from '@/components/LearningDashboard';
import userService from '@/services/userService';

const Index = () => {
  const [selectedTrack, setSelectedTrack] = useState<string | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const searchParams = useSearchParams();

  // Extract track and check authentication state
  useEffect(() => {
    const track = searchParams.get('track');
    
    if (track) {
      setSelectedTrack(track);
    }
    
    // Check authentication using user service
    const hasValidAuth = userService.hasValidAuth();
    const userData = userService.getUserData();
    
    if (hasValidAuth && userData) {
      setIsLoggedIn(true);
    } else {
      // Clear invalid auth data
      userService.clearUserData();
      setIsLoggedIn(false);
    }
    
    setIsLoading(false);
  }, [searchParams]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <Hero />
        <CourseOfferings onSelectTrack={setSelectedTrack} />
        <HowItWorks />
        {isLoggedIn && <LearningDashboard />}
      </main>
      <Footer />
    </div>
  );
};

export default Index;
