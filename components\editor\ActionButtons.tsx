import React, { useState } from "react";
import { Refresh<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ActionButtonsProps {
  onReset: () => void;
  onSkip?: () => void;
  onSubmit?: () => void;
  onRun?: () => void;
  showPreview?: boolean;
  onTogglePreview?: () => void;
  onContinueVideo?: () => void; // New prop for continuing video
  onBackToVideo?: () => void; // New prop for back to video
  fullscreenContainer?: HTMLElement | null; // Optional fullscreen portal container
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onReset,
  onSkip,
  onSubmit,
  onRun,
  showPreview,
  onTogglePreview,
  onContinueVideo,
  onBackToVideo,
  fullscreenContainer,
}) => {
  const [showSkipConfirm, setShowSkipConfirm] = useState(false);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);

  const handleSubmitClick = () => {
    // First show the confirm dialog
    setShowSubmitConfirm(true);
  };

  const handleConfirmedSubmit = () => {
    // After confirmation, run the submit handler
    if (onSubmit) onSubmit();

    // Continue video playback if handler exists
    if (onContinueVideo) {
      onContinueVideo();
    }
    // If we're not already in preview mode, toggle to preview
    else if (!showPreview && onTogglePreview) {
      onTogglePreview();
    }

    setShowSubmitConfirm(false);
  };

  return (
    <>
      {/* Separate section for Skip and Submit buttons if they exist */}
      {(onSkip || onSubmit || onReset) && (
        <div className="bg-background px-4 py-3 border-t flex justify-between items-center gap-2">
          {/* Left section - empty or could have other functionality */}
          <div>
            {/* This div can remain empty or hold other actions in the future */}
          </div>

          {/* Right section - contains Reset, Skip and Submit buttons */}
          <div className="flex items-center gap-2">
            {/* Back to Video button */}
            {onBackToVideo && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBackToVideo}
                className="text-primary hover:text-primary/80"
              >
                <ArrowLeft className="h-3.5 w-3.5 mr-1" />
                Back to Video
              </Button>
            )}
            {/* Reset button moved here */}
            {onReset && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onReset}
                className="text-muted-foreground hover:text-foreground"
              >
                <RefreshCw className="h-3.5 w-3.5 mr-1" />
                Reset
              </Button>
            )}

            {/* Optional Skip action */}
            {onSkip && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSkipConfirm(true)}
                className="text-muted-foreground hover:bg-accent hover:text-foreground"
              >
                <SkipForward className="h-3.5 w-3.5 mr-1" />
                Skip
              </Button>
            )}

            {/* Submit action */}
            {onSubmit && (
              <Button variant="secondary" size="sm" onClick={handleSubmitClick}>
                <Check className="h-3.5 w-3.5 mr-1" />
                Review
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Skip Confirmation Dialog */}
      <AlertDialog open={showSkipConfirm} onOpenChange={setShowSkipConfirm}>
        <AlertDialogContent container={fullscreenContainer}>
          <AlertDialogHeader>
            <AlertDialogTitle>Skip this challenge?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to skip this coding challenge? You can
              always come back to it later.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (onSkip) onSkip();
                setShowSkipConfirm(false);

                // Continue video playback if handler exists
                if (onContinueVideo) {
                  onContinueVideo();
                }
              }}
            >
              Yes, skip
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Submit Confirmation Dialog */}
      <AlertDialog open={showSubmitConfirm} onOpenChange={setShowSubmitConfirm}>
        <AlertDialogContent container={fullscreenContainer}>
          <AlertDialogHeader>
            <AlertDialogTitle>Submit your solution?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you ready to submit your code for evaluation?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmedSubmit}>
              Submit
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ActionButtons;
