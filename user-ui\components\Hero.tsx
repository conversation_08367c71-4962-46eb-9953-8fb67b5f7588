"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import {
  Code,
  BookOpen,
  Activity,
  Zap,
  Rocket,
  Target,
  Trophy,
  Sparkles,
} from "lucide-react";
import InfiniteMovingCards from "@/components/ui/InfiniteMovingCards";

const Hero = () => {
  return (
    <section className="py-20 px-4 sm:px-6 relative overflow-hidden">
      {/* Background gradient blur */}
      <div className="absolute -top-40 -left-40 w-80 h-80 bg-upskilleo-purple/30 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-20 -right-20 w-80 h-80 bg-upskilleo-deep-purple/20 rounded-full blur-3xl"></div>

      <div className="container mx-auto max-w-6xl relative z-10">
        <div className="flex flex-col lg:flex-row items-start justify-between gap-10">
          <div className="lg:w-1/2 space-y-6 animate-fade-in">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight">
              Level Up with <span className="text-gradient">Upskilleo</span> —
              The Interactive Way
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-xl">
              Whether you're an early-career talent aiming to become job-ready
              or a professional looking to upskill, Upskilleo's interactive
              platform empowers you to master in-demand tech skills, build
              real-world projects, and unlock opportunities at top companies.
            </p>

            <div className="flex flex-wrap gap-4 pt-4">
              <Button size="lg">Get Started</Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/try-demo">Try Demo</Link>
              </Button>
            </div>

            <div className="flex items-center space-x-2 text-sm text-muted-foreground pt-2">
              <div className="flex -space-x-2">
                {[...Array(4)].map((_, i) => (
                  <div
                    key={i}
                    className="w-8 h-8 rounded-full border-2 border-background bg-muted flex items-center justify-center"
                  >
                    <span className="text-xs font-medium">U{i + 1}</span>
                  </div>
                ))}
              </div>
              <span>Join 2,000+ learners already upskilling</span>
            </div>
          </div>

          <div className="lg:w-1/2 animate-scale-up">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-upskilleo-purple/20 to-upskilleo-deep-purple/20 rounded-xl transform rotate-1"></div>
              <div className="relative bg-card dark:bg-card shadow-xl rounded-xl border border-border p-6 backdrop-blur-sm">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Interactive Learning
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-primary/10 dark:bg-primary/20 p-2 rounded-lg">
                      <Code size={24} className="text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">Interactive Code Editor</h3>
                      <p className="text-sm text-muted-foreground">
                        Write and test code directly in your browser
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-primary/10 dark:bg-primary/20 p-2 rounded-lg">
                      <BookOpen size={24} className="text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">Guided Learning Paths</h3>
                      <p className="text-sm text-muted-foreground">
                        Follow structured roadmaps tailored to your goals
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-primary/10 dark:bg-primary/20 p-2 rounded-lg">
                      <Activity size={24} className="text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">Real-time AI Feedback</h3>
                      <p className="text-sm text-muted-foreground">
                        Get instant feedback on your code progress
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Benefit Bubbles: full-bleed, outside container */}
      <div className="w-screen relative left-1/2 right-1/2 -mx-[50vw] mt-10">
        <InfiniteMovingCards
          speed="normal"
          direction="left"
          className="gap-x-3"
        >
          <div className="group flex items-center gap-2.5 min-w-[260px] bg-upskilleo-purple/15 dark:bg-upskilleo-purple/10 backdrop-blur-sm px-4 py-2.5 rounded-full text-xs hover:shadow-md transition-all duration-300 hover:-translate-y-0.5 cursor-pointer border border-upskilleo-purple/30 hover:border-upskilleo-purple/50 hover:bg-upskilleo-purple/25">
            <Zap className="w-4 h-4 text-yellow-400 group-hover:rotate-12 transition-transform duration-300" />
            <span className="font-medium text-white">
              Master Tech Through Interactive Learning
            </span>
          </div>
          <div className="group flex items-center gap-2.5 min-w-[260px] bg-upskilleo-purple/15 dark:bg-upskilleo-purple/10 backdrop-blur-sm px-4 py-2.5 rounded-full text-xs hover:shadow-md transition-all duration-300 hover:-translate-y-0.5 cursor-pointer border border-upskilleo-purple/30 hover:border-upskilleo-purple/50 hover:bg-upskilleo-purple/25">
            <Rocket className="w-4 h-4 text-blue-400 group-hover:rotate-12 transition-transform duration-300" />
            <span className="font-medium text-white">
              Build Production-Ready Projects
            </span>
          </div>
          <div className="group flex items-center gap-2.5 min-w-[260px] bg-upskilleo-purple/15 dark:bg-upskilleo-purple/10 backdrop-blur-sm px-4 py-2.5 rounded-full text-xs hover:shadow-md transition-all duration-300 hover:-translate-y-0.5 cursor-pointer border border-upskilleo-purple/30 hover:border-upskilleo-purple/50 hover:bg-upskilleo-purple/25">
            <Target className="w-4 h-4 text-pink-400 group-hover:rotate-12 transition-transform duration-300" />
            <span className="font-medium text-white">
              Get Personalized AI-Powered Guidance
            </span>
          </div>
          <div className="group flex items-center gap-2.5 min-w-[260px] bg-upskilleo-purple/15 dark:bg-upskilleo-purple/10 backdrop-blur-sm px-4 py-2.5 rounded-full text-xs hover:shadow-md transition-all duration-300 hover:-translate-y-0.5 cursor-pointer border border-upskilleo-purple/30 hover:border-upskilleo-purple/50 hover:bg-upskilleo-purple/25">
            <Trophy className="w-4 h-4 text-orange-400 group-hover:rotate-12 transition-transform duration-300" />
            <span className="font-medium text-white">
              Land Your Dream Tech Role
            </span>
          </div>
          <div className="group flex items-center gap-2.5 min-w-[260px] bg-upskilleo-purple/15 dark:bg-upskilleo-purple/10 backdrop-blur-sm px-4 py-2.5 rounded-full text-xs hover:shadow-md transition-all duration-300 hover:-translate-y-0.5 cursor-pointer border border-upskilleo-purple/30 hover:border-upskilleo-purple/50 hover:bg-upskilleo-purple/25">
            <Sparkles className="w-4 h-4 text-emerald-400 group-hover:rotate-12 transition-transform duration-300" />
            <span className="font-medium text-white">
              Accelerate Your Career Growth
            </span>
          </div>
        </InfiniteMovingCards>
      </div>
    </section>
  );
};

export default Hero;
