
import React from "react";
import { Badge } from "@/components/ui/badge";
import { BadgeCheck } from "lucide-react";

interface SkillBadgeProps {
  skill: string;
  color?: string;
}

const colorPalette = [
  "bg-emerald-100 text-emerald-800 border-emerald-200",
  "bg-blue-100 text-blue-800 border-blue-200",
  "bg-purple-100 text-purple-800 border-purple-200",
  "bg-pink-100 text-pink-800 border-pink-200",
  "bg-yellow-100 text-yellow-800 border-yellow-200",
  "bg-orange-100 text-orange-800 border-orange-200",
  "bg-gray-100 text-gray-800 border-gray-200"
];

const SkillBadge: React.FC<SkillBadgeProps> = ({ skill, color }) => {
  // Choose a color based on the skill name or use the provided color
  const colorClass = color || colorPalette[
    skill.length % colorPalette.length
  ];

  return (
    <Badge
      variant="outline"
      className={`gap-1 px-3 py-1.5 rounded-full font-medium text-xs border ${colorClass} animate-fade-in shadow-sm hover:shadow transition-all`}
      title={skill}
    >
      <span className="inline-flex items-center">
        <BadgeCheck className="inline mr-1.5 h-4 w-4" />
      </span>
      <span className="leading-normal">{skill}</span>
    </Badge>
  );
};

export default SkillBadge;
