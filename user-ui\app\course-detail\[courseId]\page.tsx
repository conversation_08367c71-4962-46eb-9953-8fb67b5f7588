/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react/no-unescaped-entities */
"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  BookOpen,
  GraduationCap,
  Clock,
  User,
  Users,
  Star,
  TrendingUp,
  Award,
  CheckCircle,
  Play,
  Briefcase,
  ArrowRight,
  Layers,
  MessageSquare,
  Target,
  FileText,
  Check,
  Globe,
  X,
  ChevronRight,
  Lock,
  Unlock,
  Quote,
  Code,
  Github,
  RefreshCw,
  Home,
} from "lucide-react";
import PurchasedCourseView from "@/components/PurchasedCourseView";
import { Badge } from "@/components/ui/badge";
import SkillBadge from "@/components/course/SkillBadge";
import progressService from "@/services/progressService";
import CourseEnrollBox from "@/components/course/CourseEnrollBox";
import CareerBenefits from "@/components/course/CareerBenefits";
import CareerRoadmap from "@/components/course/CareerRoadmap";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Slider } from "@/components/ui/slider";
import apiService from "@/services/apiService";
import { CourseModule, CourseSection } from "@/types/course";
import userService from "@/services/userService";

interface Course {
  id: string;
  slug: string;
  title: string;
  description: string;
  longDescription: string;
  image: string;
  price: number;
  instructor: string;
  rating: number;
  reviews: number;
  students: number;
  level: string;
  lastUpdated: string;
  features: string[];
  topics: string[];
  modules: CourseModule[];
}

const CourseDetail = () => {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPurchased, setIsPurchased] = useState(false);
  const [selectedModule, setSelectedModule] = useState<CourseModule | null>(
    null
  );
  const [showInternshipPreview, setShowInternshipPreview] = useState(false);
  const [showPortfolioPreview, setShowPortfolioPreview] = useState(false);
  const [showCoachingPreview, setShowCoachingPreview] = useState(false);
  const [showCertificatePreview, setShowCertificatePreview] = useState(false);
  const [showProjectsPreview, setShowProjectsPreview] = useState(false);
  const [showBadgesPreview, setShowBadgesPreview] = useState(false);
  const [showEnrollmentPopup, setShowEnrollmentPopup] = useState(false);
  const [enrolling, setEnrolling] = useState(false);
  const courseId = params.courseId as string;
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hoursPerDay, setHoursPerDay] = useState(4);
  const [daysPerWeek, setDaysPerWeek] = useState(5);
  const [learningPace, setLearningPace] = useState("moderate");

  // Check if user is logged in
  const isLoggedIn = () => {
    return userService.isLoggedIn();
  };

  // Check if user is enrolled in this course
  const isEnrolledInCourse = () => {
    return userService.isEnrolledInCourse(courseId);
  };

  const handleEnroll = () => {
    if (!courseId) return;

    if (isLoggedIn()) {
      // User is logged in, show enrollment popup
      setShowEnrollmentPopup(true);
    } else {
      // User is not logged in, redirect to login page with enroll parameter
      router.push(
        `/auth?redirect=${encodeURIComponent(
          window.location.pathname
        )}&enroll=true`
      );
    }
  };

  const handleBuyNow = async () => {
    if (!courseId) return;

    setEnrolling(true);
    try {
      // Call the enroll API
      const response = await apiService.enrollInCourse(courseId);

      // Update user enrollment data using the service
      userService.updateEnrollmentData(courseId);

      // Sync with backend to get latest user data
      try {
        const userDetails = await apiService.getUserDetails(courseId);
        userService.updateUserDataFromAPI(userDetails.user);
      } catch (error) {
        console.error("Error syncing user data after enrollment:", error);
      }

      // Close popup and show success
      setShowEnrollmentPopup(false);
      setIsPurchased(true);
      toast.success("Successfully enrolled!", {
        description: "You now have access to all course materials.",
      });
    } catch (error) {
      console.error("Enrollment error:", error);
      toast.error("Failed to enroll. Please try again.");
    } finally {
      setEnrolling(false);
    }
  };

  const handleModuleClick = (module: CourseModule) => {
    setSelectedModule(module);
  };

  useEffect(() => {
    async function fetchCourse() {
      setLoading(true);
      setError(null);
      try {
        let courseData = null;
        if (userService.isLoggedIn()) {
          // Authenticated: check enrollment status only
          const isEnrolled = await apiService.getEnrollmentStatus(courseId);
          if (isEnrolled) {
            setIsPurchased(true);
            // Fetch user course details
            const userDetails = await apiService.getUserDetails(courseId);
            userService.updateUserDataFromAPI(userDetails.user);
            courseData = userDetails.courseEnrollment?.courseDetails;
          } else {
            // Not enrolled, show public details
            const publicRes = await apiService.getPublicCourseDetails(courseId);
            courseData = publicRes.course;
          }
        } else {
          // Not logged in, show public details
          const publicRes = await apiService.getPublicCourseDetails(courseId);
          courseData = publicRes.course;
        }
        setCourse(courseData);
      } catch (e) {
        setError("Failed to load course data.");
        setCourse(null);
      }
      setLoading(false);
    }
    if (courseId) fetchCourse();
  }, [courseId]);

  const getEstimatedDays = () => {
    if (!course) return 0;
    const totalHours = course.modules.reduce(
      (total: number, module: CourseModule) => {
        if (typeof module.duration === "string") {
          const match = module.duration.match(/(\d+)/);
          return total + (match ? parseInt(match[1]) : 0);
        }
        return total;
      },
      0
    );
    const paceMultiplier =
      learningPace === "fast" ? 0.8 : learningPace === "slow" ? 1.2 : 1;
    const totalDays = Math.ceil((totalHours / hoursPerDay) * paceMultiplier);
    return totalDays;
  };

  // Open enrollment popup if ?enroll=true is present in the URL
  useEffect(() => {
    if (searchParams.get("enroll") === "true") {
      setShowEnrollmentPopup(true);
      // Remove ?enroll=true from the URL (shallow routing)
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.delete("enroll");
      const newUrl = `${window.location.pathname}${
        newParams.toString() ? `?${newParams.toString()}` : ""
      }`;
      window.history.replaceState({}, "", newUrl);
    }
  }, [searchParams]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute -top-40 -left-40 w-[300px] sm:w-[500px] h-[300px] sm:h-[500px] bg-gradient-to-br from-upskilleo-purple/30 to-upskilleo-deep-purple/40 rounded-full blur-3xl animate-float-slow"></div>
          <div
            className="absolute -bottom-20 -right-20 w-[300px] sm:w-[500px] h-[300px] sm:h-[500px] bg-gradient-to-br from-upskilleo-purple/30 to-upskilleo-deep-purple/40 rounded-full blur-3xl animate-float-slow"
            style={{ animationDelay: "1s" }}
          ></div>
          <div
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] sm:w-[1000px] h-[600px] sm:h-[1000px] rounded-full blur-3xl animate-float-slow"
            style={{ animationDelay: "2s" }}
          ></div>
        </div>

        {/* Interactive floating elements */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                opacity: Math.random() * 0.4 + 0.2,
              }}
            />
          ))}
        </div>

        {/* Loading content */}
        <div className="relative z-10 text-center space-y-6">
          {/* Logo/Icon */}
          <div className="relative">
            <div className="w-20 h-20 sm:w-24 sm:h-24 mx-auto rounded-2xl bg-gradient-to-br from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach flex items-center justify-center shadow-2xl animate-pulse">
              <BookOpen className="w-10 h-10 sm:w-12 sm:h-12 text-white" />
            </div>
            {/* Pulsing ring */}
            <div className="absolute inset-0 rounded-2xl border-2 border-upskilleo-purple/30 animate-ping"></div>
          </div>

          {/* Loading text */}
          <div className="space-y-2">
            <h2 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach bg-clip-text text-transparent animate-gradient">
              Loading Your Course
            </h2>
            <p className="text-muted-foreground text-sm sm:text-base">
              Preparing your learning journey...
            </p>
          </div>

          {/* Loading dots */}
          <div className="flex justify-center space-x-2">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="w-3 h-3 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple rounded-full animate-bounce"
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}
          </div>

          {/* Progress bar */}
          <div className="w-64 sm:w-80 mx-auto">
            <div className="h-2 bg-upskilleo-soft-purple/20 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple rounded-full animate-pulse"
                style={{ width: "60%" }}
              ></div>
            </div>
          </div>

          {/* Loading message */}
          <div className="text-xs text-muted-foreground animate-pulse">
            Fetching course content...
          </div>
        </div>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute -top-40 -left-40 w-[300px] sm:w-[500px] h-[300px] sm:h-[500px] bg-gradient-to-br from-red-500/20 to-orange-500/30 rounded-full blur-3xl animate-float-slow"></div>
          <div
            className="absolute -bottom-20 -right-20 w-[300px] sm:w-[500px] h-[300px] sm:h-[500px] bg-gradient-to-br from-red-500/20 to-orange-500/30 rounded-full blur-3xl animate-float-slow"
            style={{ animationDelay: "1s" }}
          ></div>
        </div>

        {/* Error content */}
        <div className="relative z-10 text-center space-y-6 max-w-md mx-auto px-4">
          {/* Error Icon */}
          <div className="relative">
            <div className="w-20 h-20 sm:w-24 sm:h-24 mx-auto rounded-2xl bg-gradient-to-br from-red-500 via-orange-500 to-red-600 flex items-center justify-center shadow-2xl">
              <X className="w-10 h-10 sm:w-12 sm:h-12 text-white" />
            </div>
            {/* Pulsing ring */}
            <div className="absolute inset-0 rounded-2xl border-2 border-red-500/30 animate-ping"></div>
          </div>

          {/* Error text */}
          <div className="space-y-2">
            <h2 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-red-500 via-orange-500 to-red-600 bg-clip-text text-transparent">
              Oops! Something went wrong
            </h2>
            <p className="text-muted-foreground text-sm sm:text-base">
              {error || "Course not found"}
            </p>
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={() => window.location.reload()}
              className="bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white hover:opacity-90 transition-all"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push("/")}
              className="border-upskilleo-purple text-upskilleo-purple hover:bg-upskilleo-soft-purple"
            >
              <Home className="w-4 h-4 mr-2" />
              Go Home
            </Button>
          </div>

          {/* Help text */}
          <div className="text-xs text-muted-foreground">
            If the problem persists, please contact support
          </div>
        </div>
      </div>
    );
  }

  if (isPurchased) {
    return (
      <PurchasedCourseView
        courseId={courseId}
        courseTitle={course.title}
        courseDescription={course.description}
        courseImage={course.image}
        modules={course.modules}
      />
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      {/* Hero Section with Career Transformation Focus */}
      <div className="relative min-h-[calc(100vh-4rem)] flex items-center overflow-hidden ">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute -top-40 -left-40 w-[300px] sm:w-[500px] h-[300px] sm:h-[500px] bg-gradient-to-br from-upskilleo-purple/30 to-upskilleo-deep-purple/40 rounded-full blur-3xl animate-float-slow"></div>
          <div
            className="absolute -bottom-20 -right-20 w-[300px] sm:w-[500px] h-[300px] sm:h-[500px] bg-gradient-to-br from-upskilleo-purple/30 to-upskilleo-deep-purple/40 rounded-full blur-3xl animate-float-slow"
            style={{ animationDelay: "1s" }}
          ></div>
          <div
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] sm:w-[1000px] h-[600px] sm:h-[1000px]  rounded-full blur-3xl animate-float-slow"
            style={{ animationDelay: "2s" }}
          ></div>
        </div>

        {/* Interactive floating elements */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                opacity: Math.random() * 0.4 + 0.2,
              }}
            />
          ))}
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-16 items-center">
            <div className="space-y-4 sm:space-y-6 animate-fade-in">
              <div className="space-y-3 sm:space-y-4">
                <div className="flex flex-wrap items-center gap-3 sm:gap-4">
                  <Badge className="bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white px-4 sm:px-6 py-1.5 sm:py-2 rounded-full shadow-lg text-sm sm:text-base hover:scale-105 transition-transform cursor-pointer">
                    Transform Your Career
                  </Badge>
                  <div className="flex items-center bg-white/10 backdrop-blur-sm px-3 sm:px-4 py-1.5 sm:py-2 rounded-full hover:bg-white/20 transition-all cursor-pointer group">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 sm:w-5 sm:h-5 transition-transform ${
                            i < Math.floor(course.rating)
                              ? "text-upskilleo-purple group-hover:scale-110"
                              : "text-muted"
                          }`}
                          style={{ animationDelay: `${i * 0.1}s` }}
                        />
                      ))}
                    </div>
                    <span className="ml-2 text-upskilleo-deep-purple font-medium text-sm sm:text-base">
                      {course.rating} ({course.reviews} reviews)
                    </span>
                  </div>
                </div>

                <h1 className="text-3xl sm:text-5xl lg:text-6xl font-bold tracking-tight leading-tight hover:scale-[1.02] transition-transform cursor-default">
                  <span className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach animate-gradient">
                    {course.title}
                  </span>
                </h1>

                <p className="text-base sm:text-lg text-muted-foreground leading-relaxed">
                  {course.description}
                </p>

                <div className="mt-8 flex flex-col sm:flex-row gap-4">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 sm:w-5 sm:h-5 text-upskilleo-purple mr-2 group-hover:scale-110 transition-transform" />
                    <span className="text-upskilleo-deep-purple font-medium text-sm">
                      {course.students.toLocaleString()}+ enrolled
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 sm:w-5 sm:h-5 text-upskilleo-purple mr-2 group-hover:scale-110 transition-transform" />
                    <span className="text-upskilleo-deep-purple font-medium text-sm">
                      {course.level}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
                <div className="text-center sm:text-left">
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-3xl sm:text-4xl font-bold text-white hover:scale-110 transition-transform cursor-default inline-block">
                      ${course.price}
                    </span>
                    <Badge className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1.5 text-xs font-bold shadow-lg">
                      <div className="flex items-center gap-1.5">
                        <TrendingUp className="w-3 h-3" />
                        SAVE 50%
                      </div>
                    </Badge>
                  </div>
                  <span className="text-white/80 text-sm sm:text-base">
                    one-time payment • Lifetime access
                  </span>
                </div>
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white px-6 sm:px-8 py-5 sm:py-6 text-base sm:text-lg font-semibold hover:opacity-90 transform hover:scale-105 transition-transform shadow-lg hover:shadow-xl relative overflow-hidden group"
                  onClick={handleEnroll}
                >
                  {/* Shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                  <span className="relative z-10 flex items-center gap-2">
                    <Play className="w-5 h-5 sm:w-6 sm:h-6" />
                    Get Instant Access
                  </span>
                </Button>
              </div>
            </div>

            <div className="relative mt-8 sm:mt-0 group">
              <div className="aspect-video rounded-2xl sm:rounded-3xl overflow-hidden shadow-2xl border-2 border-upskilleo-purple/20 transform hover:scale-105 transition-transform hover:shadow-2xl">
                <img
                  src={course.image}
                  alt="Course Preview"
                  className="w-full h-full object-cover transition-transform group-hover:scale-110 duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-background/90 to-transparent flex items-center justify-center">
                  <Button className="bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white px-6 sm:px-8 py-4 sm:py-5 rounded-full transform hover:scale-110 transition-transform shadow-lg hover:shadow-xl group">
                    <Play className="w-5 h-5 sm:w-6 sm:h-6 mr-2 group-hover:scale-110 transition-transform" />
                    Watch Preview
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Learning Roadmap Section */}
      <div className="py-6 sm:py-8 bg-gradient-to-b from-background to-upskilleo-soft-gray/30">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
          <div className="text-center mb-4 sm:mb-6">
            <h2 className="text-xl sm:text-2xl font-bold mb-1 sm:mb-2 hover:scale-105 transition-transform cursor-default inline-block">
              <span className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach">
                Your Learning Roadmap
              </span>
            </h2>
            <p className="text-sm sm:text-base text-muted-foreground">
              A structured path to master your skills
            </p>
          </div>

          <div className="relative">
            <div className="space-y-3 sm:space-y-4">
              {course.modules.map((module: CourseModule, index: number) => (
                <div key={module.id} className="relative pl-12 pb-8">
                  {/* Timeline line and milestone marker */}
                  <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-muted"></div>

                  {/* Milestone marker */}
                  <div className="absolute left-4 -translate-x-1/2 w-6 h-6 rounded-full flex items-center justify-center z-10 bg-gradient-to-br from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach text-white ring-2 ring-upskilleo-purple/20 shadow-lg hover:scale-110 transition-transform">
                    <span className="text-xs font-medium">{index + 1}</span>
                  </div>

                  <Card className="bg-background/50 backdrop-blur-sm border-border hover:shadow-lg transition-all">
                    <CardContent className="p-3 sm:p-4">
                      <div className="space-y-2 sm:space-y-3">
                        <div>
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                            <h3 className="text-base sm:text-lg font-semibold text-upskilleo-purple">
                              {module.title}
                            </h3>
                            <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                                <span>{module.duration}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <BookOpen className="w-3 h-3 sm:w-4 sm:h-4" />
                                <span>{module.totalLessons}</span>
                              </div>
                            </div>
                          </div>
                          <p className="text-xs sm:text-sm text-muted-foreground mt-1 line-clamp-2">
                            {module.description}
                          </p>
                        </div>

                        {module.topics && (
                          <div className="flex flex-wrap gap-1.5 sm:gap-2">
                            {module.topics
                              .slice(0, 2)
                              .map((topic: string, topicIndex: number) => (
                                <div
                                  key={topicIndex}
                                  className="flex items-center gap-1 px-2 py-0.5 rounded-full bg-upskilleo-soft-purple/10 text-[10px] sm:text-xs"
                                >
                                  <Check className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-upskilleo-purple" />
                                  <span className="truncate max-w-[120px] sm:max-w-[200px]">
                                    {topic}
                                  </span>
                                </div>
                              ))}
                            {module.topics.length > 2 && (
                              <div className="px-2 py-0.5 rounded-full bg-upskilleo-soft-purple/10 text-[10px] sm:text-xs text-muted-foreground">
                                +{module.topics.length - 2} more
                              </div>
                            )}
                          </div>
                        )}

                        {module.sections && (
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1.5 sm:gap-2">
                            {module.sections
                              .slice(0, 2)
                              .map((section: CourseSection) => (
                                <div
                                  key={section.id}
                                  className="flex items-center gap-1.5 p-1.5 sm:p-2 rounded-lg bg-upskilleo-soft-purple/5 text-[10px] sm:text-xs"
                                >
                                  <Play className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-upskilleo-purple flex-shrink-0" />
                                  <span className="truncate">
                                    {section.title}
                                  </span>
                                  <span className="text-muted-foreground ml-auto flex-shrink-0">
                                    {section.duration}
                                  </span>
                                </div>
                              ))}
                            {module.sections.length > 2 && (
                              <div className="p-1.5 sm:p-2 rounded-lg bg-upskilleo-soft-purple/5 text-[10px] sm:text-xs text-muted-foreground">
                                +{module.sections.length - 2} more lessons
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Success Path Section */}
      <div className="py-16 bg-gradient-to-b from-background to-upskilleo-soft-gray/30 border-t border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Badge className="bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white px-4 py-1.5 rounded-full shadow-lg mb-4 hover:scale-105 transition-transform cursor-pointer">
              Your Journey to Success
            </Badge>
            <h2 className="text-3xl sm:text-4xl font-bold mb-4 hover:scale-105 transition-transform cursor-default inline-block">
              <span className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach">
                Your Success Path
              </span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Transform your career with industry-recognized credentials and
              real-world opportunities
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Career Opportunities */}
            <Card className="bg-background/50 backdrop-blur-sm border-border hover:shadow-xl transition-all hover:scale-[1.02] group overflow-hidden">
              <CardContent className="p-6 relative">
                {/* Decorative elements */}
                <div className="absolute -right-10 -top-10 w-40 h-40 bg-gradient-to-br from-upskilleo-purple/10 to-upskilleo-deep-purple/10 rounded-full blur-2xl group-hover:scale-150 transition-transform duration-700"></div>
                <div className="absolute -left-10 -bottom-10 w-40 h-40 bg-gradient-to-tr from-upskilleo-purple/10 to-upskilleo-deep-purple/10 rounded-full blur-2xl group-hover:scale-150 transition-transform duration-700"></div>

                <div className="relative">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center group-hover:scale-110 transition-transform shadow-lg">
                      <Briefcase className="w-7 h-7 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-1 group-hover:text-upskilleo-purple transition-colors">
                        Career Opportunities
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Launch your career with real opportunities
                      </p>
                    </div>
                  </div>

                  <ul className="space-y-4">
                    <li
                      className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all duration-300 cursor-pointer active:scale-[0.98]"
                      onClick={() => setShowInternshipPreview(true)}
                    >
                      {/* Hover effect background */}
                      <div className="absolute inset-0 bg-gradient-to-r from-upskilleo-purple/0 via-upskilleo-purple/5 to-upskilleo-purple/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

                      <div className="relative flex items-start gap-3 p-3">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:scale-110 transition-transform">
                          <Briefcase className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">
                              Guaranteed Internship Placement
                            </span>
                            <ChevronRight className="w-4 h-4 text-upskilleo-purple/50 group-hover:translate-x-0.5 transition-transform" />
                          </div>
                          <span className="text-sm text-muted-foreground block">
                            We're committed to your success. That's why we
                            guarantee an internship placement upon course
                            completion.
                          </span>
                          <div className="mt-2 flex items-center gap-2 text-xs text-upskilleo-purple">
                            <div className="flex items-center gap-1">
                              <Check className="w-3 h-3" />
                              <span>Click to learn more</span>
                            </div>
                            <div className="h-3 w-[1px] bg-upskilleo-purple/20"></div>
                            <div className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              <span>85% success rate</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                    <li
                      className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all duration-300 cursor-pointer active:scale-[0.98]"
                      onClick={() => setShowCoachingPreview(true)}
                    >
                      {/* Hover effect background */}
                      <div className="absolute inset-0 bg-gradient-to-r from-upskilleo-purple/0 via-upskilleo-purple/5 to-upskilleo-purple/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

                      <div className="relative flex items-start gap-3 p-3">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:scale-110 transition-transform">
                          <MessageSquare className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">
                              1:1 Career Coaching
                            </span>
                            <ChevronRight className="w-4 h-4 text-upskilleo-purple/50 group-hover:translate-x-0.5 transition-transform" />
                          </div>
                          <span className="text-sm text-muted-foreground block">
                            Get personalized guidance from industry experts to
                            land your dream job
                          </span>
                          <div className="mt-2 flex items-center gap-2 text-xs text-upskilleo-purple">
                            <div className="flex items-center gap-1">
                              <Check className="w-3 h-3" />
                              <span>Click to learn more</span>
                            </div>
                            <div className="h-3 w-[1px] bg-upskilleo-purple/20"></div>
                            <div className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              <span>95% success rate</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                    <li
                      className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all duration-300 cursor-pointer active:scale-[0.98]"
                      onClick={() => setShowPortfolioPreview(true)}
                    >
                      {/* Hover effect background */}
                      <div className="absolute inset-0 bg-gradient-to-r from-upskilleo-purple/0 via-upskilleo-purple/5 to-upskilleo-purple/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

                      <div className="relative flex items-start gap-3 p-3">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:scale-110 transition-transform">
                          <Globe className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">
                              Free Professional Portfolio
                            </span>
                            <ChevronRight className="w-4 h-4 text-upskilleo-purple/50 group-hover:translate-x-0.5 transition-transform" />
                          </div>
                          <span className="text-sm text-muted-foreground block">
                            Get a stunning portfolio website that makes
                            recruiters reach out to you
                          </span>
                          <div className="mt-2 flex items-center gap-2 text-xs text-upskilleo-purple">
                            <div className="flex items-center gap-1">
                              <Check className="w-3 h-3" />
                              <span>Click to preview</span>
                            </div>
                            <div className="h-3 w-[1px] bg-upskilleo-purple/20"></div>
                            <div className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              <span>Customizable</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Learning & Certification */}
            <Card className="bg-background/50 backdrop-blur-sm border-border hover:shadow-xl transition-all hover:scale-[1.02] group overflow-hidden">
              <CardContent className="p-6 relative">
                {/* Decorative elements */}
                <div className="absolute -right-10 -top-10 w-40 h-40 bg-gradient-to-br from-upskilleo-purple/10 to-upskilleo-deep-purple/10 rounded-full blur-2xl group-hover:scale-150 transition-transform duration-700"></div>
                <div className="absolute -left-10 -bottom-10 w-40 h-40 bg-gradient-to-tr from-upskilleo-purple/10 to-upskilleo-deep-purple/10 rounded-full blur-2xl group-hover:scale-150 transition-transform duration-700"></div>

                <div className="relative">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center group-hover:scale-110 transition-transform shadow-lg">
                      <Award className="w-7 h-7 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-1 group-hover:text-upskilleo-purple transition-colors">
                        Learning & Certification
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Industry-recognized credentials to boost your career
                      </p>
                    </div>
                  </div>

                  <ul className="space-y-4">
                    <li
                      className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all duration-300 cursor-pointer active:scale-[0.98]"
                      onClick={() => setShowCertificatePreview(true)}
                    >
                      {/* Hover effect background */}
                      <div className="absolute inset-0 bg-gradient-to-r from-upskilleo-purple/0 via-upskilleo-purple/5 to-upskilleo-purple/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

                      <div className="relative flex items-start gap-3 p-3">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:scale-110 transition-transform">
                          <Award className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">
                              Industry-Recognized Certificate
                            </span>
                            <ChevronRight className="w-4 h-4 text-upskilleo-purple/50 group-hover:translate-x-0.5 transition-transform" />
                          </div>
                          <span className="text-sm text-muted-foreground block">
                            Stand out with a certificate trusted by top tech
                            companies
                          </span>
                          <div className="mt-2 flex items-center gap-2 text-xs text-upskilleo-purple">
                            <div className="flex items-center gap-1">
                              <Check className="w-3 h-3" />
                              <span>Click to learn more</span>
                            </div>
                            <div className="h-3 w-[1px] bg-upskilleo-purple/20"></div>
                            <div className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              <span>Trusted by 200+ companies</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>

                    <li
                      className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all duration-300 cursor-pointer active:scale-[0.98]"
                      onClick={() => setShowProjectsPreview(true)}
                    >
                      {/* Hover effect background */}
                      <div className="absolute inset-0 bg-gradient-to-r from-upskilleo-purple/0 via-upskilleo-purple/5 to-upskilleo-purple/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

                      <div className="relative flex items-start gap-3 p-3">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:scale-110 transition-transform">
                          <Layers className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">
                              Real-World Project Experience
                            </span>
                            <ChevronRight className="w-4 h-4 text-upskilleo-purple/50 group-hover:translate-x-0.5 transition-transform" />
                          </div>
                          <span className="text-sm text-muted-foreground block">
                            Build production-ready projects that employers
                            actually want to see
                          </span>
                          <div className="mt-2 flex items-center gap-2 text-xs text-upskilleo-purple">
                            <div className="flex items-center gap-1">
                              <Check className="w-3 h-3" />
                              <span>Click to learn more</span>
                            </div>
                            <div className="h-3 w-[1px] bg-upskilleo-purple/20"></div>
                            <div className="flex items-center gap-1">
                              <Code className="w-3 h-3" />
                              <span>6+ projects</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>

                    <li
                      className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all duration-300 cursor-pointer active:scale-[0.98]"
                      onClick={() => setShowBadgesPreview(true)}
                    >
                      {/* Hover effect background */}
                      <div className="absolute inset-0 bg-gradient-to-r from-upskilleo-purple/0 via-upskilleo-purple/5 to-upskilleo-purple/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

                      <div className="relative flex items-start gap-3 p-3">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:scale-110 transition-transform">
                          <Check className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">
                              Verified Skill Badges
                            </span>
                            <ChevronRight className="w-4 h-4 text-upskilleo-purple/50 group-hover:translate-x-0.5 transition-transform" />
                          </div>
                          <span className="text-sm text-muted-foreground block">
                            Earn LinkedIn-ready skill badges that catch
                            recruiters' attention
                          </span>
                          <div className="mt-2 flex items-center gap-2 text-xs text-upskilleo-purple">
                            <div className="flex items-center gap-1">
                              <Check className="w-3 h-3" />
                              <span>Click to learn more</span>
                            </div>
                            <div className="h-3 w-[1px] bg-upskilleo-purple/20"></div>
                            <div className="flex items-center gap-1">
                              <Award className="w-3 h-3" />
                              <span>12+ badges</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Success Stats */}
          <div className="mt-12 grid grid-cols-1 sm:grid-cols-3 gap-6">
            <Card className="bg-background/50 backdrop-blur-sm border-border hover:shadow-lg transition-all hover:scale-[1.02]">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-upskilleo-purple mb-2">
                  98%
                </div>
                <p className="text-sm text-muted-foreground">
                  Course Completion Rate
                </p>
              </CardContent>
            </Card>
            <Card className="bg-background/50 backdrop-blur-sm border-border hover:shadow-lg transition-all hover:scale-[1.02]">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-upskilleo-purple mb-2">
                  85%
                </div>
                <p className="text-sm text-muted-foreground">
                  Career Transition Success
                </p>
              </CardContent>
            </Card>
            <Card className="bg-background/50 backdrop-blur-sm border-border hover:shadow-lg transition-all hover:scale-[1.02]">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-upskilleo-purple mb-2">
                  200+
                </div>
                <p className="text-sm text-muted-foreground">
                  Partner Companies
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Key Features Grid */}
      <div className="py-8 sm:py-12 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            {course.features
              .slice(0, 4)
              .map((feature: string, index: number) => (
                <Card
                  key={index}
                  className="bg-background/50 backdrop-blur-sm border-border hover:shadow-xl transition-all hover:scale-[1.02] cursor-pointer group"
                >
                  <CardContent className="p-4 sm:p-6">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center group-hover:scale-110 transition-transform">
                        <Check className="w-5 h-5 text-white" />
                      </div>
                      <p className="text-sm sm:text-base font-medium group-hover:text-upskilleo-purple transition-colors">
                        {feature}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </div>
      </div>

      {/* Learning Pace Calculator */}
      <div className="py-8 sm:py-12 bg-gradient-to-b from-background to-upskilleo-soft-gray/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-6">
            <h2 className="text-xl sm:text-2xl font-bold mb-2 hover:scale-105 transition-transform cursor-default inline-block">
              <span className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach">
                Plan Your Learning Journey
              </span>
            </h2>
            <p className="text-sm sm:text-base text-muted-foreground">
              Customize your study schedule to fit your lifestyle
            </p>
          </div>

          <Card className="bg-gradient-to-r from-card/80 to-card/60 backdrop-blur-sm border border-primary/20 shadow-lg max-w-4xl mx-auto">
            <CardContent className="p-6">
              <div className="space-y-6">
                {/* Controls */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {/* Hours per day */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-foreground">
                        Study Hours per Day
                      </span>
                      <div className="px-3 py-1 bg-primary/10 rounded-full">
                        <span className="text-sm font-bold text-primary">
                          {hoursPerDay}h
                        </span>
                      </div>
                    </div>
                    <div className="relative">
                      <Slider
                        value={[hoursPerDay]}
                        min={1}
                        max={8}
                        step={1}
                        onValueChange={([value]: number[]) =>
                          setHoursPerDay(value)
                        }
                        className="h-3"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground mt-1">
                        <span>1h</span>
                        <span>8h</span>
                      </div>
                    </div>
                  </div>

                  {/* Days per week */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-foreground">
                        Study Days per Week
                      </span>
                      <div className="px-3 py-1 bg-primary/10 rounded-full">
                        <span className="text-sm font-bold text-primary">
                          {daysPerWeek}d
                        </span>
                      </div>
                    </div>
                    <div className="relative">
                      <Slider
                        value={[daysPerWeek]}
                        min={1}
                        max={7}
                        step={1}
                        onValueChange={([value]: number[]) =>
                          setDaysPerWeek(value)
                        }
                        className="h-3"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground mt-1">
                        <span>1d</span>
                        <span>7d</span>
                      </div>
                    </div>
                  </div>

                  {/* Learning pace */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-foreground">
                        Learning Pace
                      </span>
                    </div>
                    <div className="flex gap-2">
                      {[
                        {
                          value: "slow",
                          label: "Slow",
                          color: "bg-orange-500",
                        },
                        {
                          value: "moderate",
                          label: "Moderate",
                          color: "bg-primary",
                        },
                        { value: "fast", label: "Fast", color: "bg-green-500" },
                      ].map(({ value, label, color }) => (
                        <Button
                          key={value}
                          variant={
                            learningPace === value ? "default" : "outline"
                          }
                          size="sm"
                          className={`flex-1 h-8 text-xs font-medium transition-all ${
                            learningPace === value
                              ? `${color} text-white border-0`
                              : "hover:bg-primary/10"
                          }`}
                          onClick={() => setLearningPace(value)}
                        >
                          {label}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Result */}
                <div className="pt-4 border-t border-primary/10">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Estimated completion time
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Based on your current settings
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="px-6 py-3 bg-gradient-to-r from-primary/20 to-primary/10 rounded-lg border border-primary/20">
                        <p className="text-3xl font-bold text-primary">
                          {getEstimatedDays()}
                        </p>
                        <p className="text-sm text-muted-foreground">days</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Enrollment CTA */}
      <div className="py-8 sm:py-12 bg-background border-t border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach p-6 sm:p-8 hover:shadow-2xl transition-all hover:scale-[1.02]">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 items-center">
              <div>
                <h2 className="text-2xl sm:text-3xl font-bold text-white mb-3 hover:scale-105 transition-transform cursor-default inline-block">
                  Start Your Learning Journey Today
                </h2>
                <p className="text-white/90 text-base sm:text-lg">
                  Join {course.students.toLocaleString()}+ students who have
                  already transformed their careers
                </p>
              </div>
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
                <div className="text-center sm:text-left">
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-3xl sm:text-4xl font-bold text-white hover:scale-110 transition-transform cursor-default inline-block">
                      ${course.price}
                    </span>
                    <Badge className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1.5 text-xs font-bold shadow-lg">
                      <div className="flex items-center gap-1.5">
                        <TrendingUp className="w-3 h-3" />
                        SAVE 50%
                      </div>
                    </Badge>
                  </div>
                  <span className="text-white/80 text-sm sm:text-base">
                    one-time payment • Lifetime access
                  </span>
                </div>
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white px-6 sm:px-8 py-5 sm:py-6 text-base sm:text-lg font-semibold hover:opacity-90 transform hover:scale-105 transition-transform shadow-lg hover:shadow-xl relative overflow-hidden group"
                  onClick={handleEnroll}
                >
                  {/* Shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                  <span className="relative z-10 flex items-center gap-2">
                    <Play className="w-5 h-5 sm:w-6 sm:h-6" />
                    Get Instant Access
                  </span>
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Internship Preview Dialog */}
      <Dialog
        open={showInternshipPreview}
        onOpenChange={setShowInternshipPreview}
      >
        <DialogContent className="w-[95vw] max-w-[400px] bg-background/95 backdrop-blur-sm p-0 overflow-hidden rounded-xl sm:rounded-lg">
          {/* Hero Section */}
          <div className="relative p-4 sm:p-5 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple text-white">
            <div className="relative">
              <h2 className="text-lg sm:text-xl font-bold">
                Guaranteed Internship Path
              </h2>
              <p className="text-sm text-white/90 mt-0.5">
                From internship to full-time offer
              </p>
            </div>
          </div>

          <div className="p-4 sm:p-5 space-y-4">
            {/* Success Story */}
            <div className="relative overflow-hidden rounded-lg">
              {/* Decorative background elements */}
              <div className="absolute inset-0 bg-gradient-to-br from-upskilleo-purple/10 via-upskilleo-deep-purple/5 to-upskilleo-purple/10">
                <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10"></div>
                <div className="absolute -right-10 -top-10 w-40 h-40 bg-gradient-to-br from-upskilleo-purple/20 to-upskilleo-deep-purple/20 rounded-full blur-2xl"></div>
                <div className="absolute -left-10 -bottom-10 w-40 h-40 bg-gradient-to-tr from-upskilleo-purple/20 to-upskilleo-deep-purple/20 rounded-full blur-2xl"></div>
              </div>

              {/* Content */}
              <div className="relative p-4 space-y-3">
                <div className="flex justify-center">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center">
                    <Quote className="w-4 h-4 text-white" />
                  </div>
                </div>
                <p className="text-sm font-medium leading-relaxed text-center">
                  "Started as an intern, got a full-time offer within 3 months.
                  The career support team helped me every step of the way."
                </p>
                <div className="flex items-center justify-center gap-2 pt-1">
                  <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center text-white text-xs font-medium ring-2 ring-upskilleo-purple/20">
                    SK
                  </div>
                  <p className="text-xs font-medium">
                    Sarah K. • Full Stack Developer
                  </p>
                </div>
              </div>
            </div>

            {/* Quick Path */}
            <div className="grid grid-cols-2 gap-2">
              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <span className="text-white text-xs sm:text-sm font-medium">
                    1
                  </span>
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  Complete Course
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  Master in-demand skills
                </p>
              </div>

              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <span className="text-white text-xs sm:text-sm font-medium">
                    2
                  </span>
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  Start Internship
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  With partner companies
                </p>
              </div>

              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <span className="text-white text-xs sm:text-sm font-medium">
                    3
                  </span>
                </div>
                <h4 className="font-medium text-xs sm:text-sm">Get Mentored</h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  1:1 career guidance
                </p>
              </div>

              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <span className="text-white text-xs sm:text-sm font-medium">
                    4
                  </span>
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  Land Full-Time
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  Convert to permanent role
                </p>
              </div>
            </div>

            {/* Trust Badge */}
            <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 p-2.5 cursor-pointer transition-all duration-300 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 active:scale-[0.98]">
              {/* Hover effect background */}
              <div className="absolute inset-0 bg-gradient-to-r from-upskilleo-purple/0 via-upskilleo-purple/5 to-upskilleo-purple/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

              <div className="relative flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <Check className="w-4 h-4 text-upskilleo-purple group-hover:scale-110 transition-transform" />
                  <span className="text-xs sm:text-sm">
                    85% Internship to Full-Time Rate
                  </span>
                </div>
                <ChevronRight className="w-4 h-4 text-upskilleo-purple/50 group-hover:translate-x-0.5 transition-transform" />
              </div>
            </div>

            {/* CTA */}
            <Button
              className="w-full bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white py-4 sm:py-5 text-sm sm:text-base font-semibold hover:opacity-90 active:scale-[0.98] transition-transform"
              onClick={handleEnroll}
            >
              Enroll Now
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Career Coaching Preview Dialog */}
      <Dialog open={showCoachingPreview} onOpenChange={setShowCoachingPreview}>
        <DialogContent className="w-[95vw] max-w-[400px] bg-background/95 backdrop-blur-sm p-0 overflow-hidden rounded-xl sm:rounded-lg">
          {/* Hero Section */}
          <div className="relative p-4 sm:p-5 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple text-white">
            <div className="relative">
              <h2 className="text-lg sm:text-xl font-bold">
                1:1 Career Coaching
              </h2>
              <p className="text-sm text-white/90 mt-0.5">
                Personalized guidance to launch your career
              </p>
            </div>
          </div>

          <div className="p-4 sm:p-5 space-y-4">
            {/* Success Story */}
            <div className="relative overflow-hidden rounded-lg">
              {/* Decorative background elements */}
              <div className="absolute inset-0 bg-gradient-to-br from-upskilleo-purple/10 via-upskilleo-deep-purple/5 to-upskilleo-purple/10">
                <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10"></div>
                <div className="absolute -right-10 -top-10 w-40 h-40 bg-gradient-to-br from-upskilleo-purple/20 to-upskilleo-deep-purple/20 rounded-full blur-2xl"></div>
                <div className="absolute -left-10 -bottom-10 w-40 h-40 bg-gradient-to-tr from-upskilleo-purple/20 to-upskilleo-deep-purple/20 rounded-full blur-2xl"></div>
              </div>

              {/* Content */}
              <div className="relative p-4 space-y-3">
                <div className="flex justify-center">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center">
                    <Quote className="w-4 h-4 text-white" />
                  </div>
                </div>
                <p className="text-sm font-medium leading-relaxed text-center">
                  "My career coach helped me identify my strengths and craft a
                  compelling story. Landed my dream job at a top tech company
                  within 2 months!"
                </p>
                <div className="flex items-center justify-center gap-2 pt-1">
                  <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center text-white text-xs font-medium ring-2 ring-upskilleo-purple/20">
                    MJ
                  </div>
                  <p className="text-xs font-medium">
                    Michael J. • Junior Developer
                  </p>
                </div>
              </div>
            </div>

            {/* Coaching Features */}
            <div className="grid grid-cols-2 gap-2">
              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <User className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  Resume Review
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  Get expert feedback
                </p>
              </div>

              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <MessageSquare className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  Interview Prep
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  Mock interviews & tips
                </p>
              </div>

              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <Target className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  Career Planning
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  Set clear goals
                </p>
              </div>

              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <TrendingUp className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-medium text-xs sm:text-sm">Job Search</h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  Find opportunities
                </p>
              </div>
            </div>

            {/* Trust Badge */}
            <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 p-2.5 cursor-pointer transition-all duration-300 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 active:scale-[0.98]">
              {/* Hover effect background */}
              <div className="absolute inset-0 bg-gradient-to-r from-upskilleo-purple/0 via-upskilleo-purple/5 to-upskilleo-purple/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

              <div className="relative flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <Check className="w-4 h-4 text-upskilleo-purple group-hover:scale-110 transition-transform" />
                  <span className="text-xs sm:text-sm">
                    95% Job Placement Success Rate
                  </span>
                </div>
                <ChevronRight className="w-4 h-4 text-upskilleo-purple/50 group-hover:translate-x-0.5 transition-transform" />
              </div>
            </div>

            {/* CTA */}
            <Button
              className="w-full bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white py-4 sm:py-5 text-sm sm:text-base font-semibold hover:opacity-90 active:scale-[0.98] transition-transform"
              onClick={handleEnroll}
            >
              Start Your Career Journey
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Portfolio Preview Dialog */}
      <Dialog
        open={showPortfolioPreview}
        onOpenChange={setShowPortfolioPreview}
      >
        <DialogContent className="w-[95vw] max-w-[500px] h-[90vh] max-h-[800px] bg-background p-0 overflow-hidden rounded-xl sm:rounded-lg flex flex-col">
          {/* Header */}
          <div className="relative p-3 sm:p-4 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple text-white border-b border-white/10">
            <div className="relative">
              <h2 className="text-base sm:text-lg font-bold">
                Professional Portfolio Preview
              </h2>
              <p className="text-xs sm:text-sm text-white/90 mt-0.5">
                Your personalized portfolio website
              </p>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="p-3 sm:p-4 space-y-4">
              {/* Portfolio Preview */}
              <div className="relative rounded-lg overflow-hidden border border-border bg-white shadow-lg">
                {/* Browser-like header */}
                <div className="flex items-center gap-2 px-3 py-2 bg-gray-100 border-b border-border">
                  <div className="flex gap-1.5">
                    <div className="w-2.5 h-2.5 rounded-full bg-red-400"></div>
                    <div className="w-2.5 h-2.5 rounded-full bg-yellow-400"></div>
                    <div className="w-2.5 h-2.5 rounded-full bg-green-400"></div>
                  </div>
                  <div className="flex-1 mx-3 h-5 bg-gray-200 rounded-full flex items-center px-3 text-xs text-gray-500 font-mono">
                    portfolio.dev/john-doe
                  </div>
                </div>

                {/* Portfolio Content Preview */}
                <div className="p-3 sm:p-4">
                  {/* Hero Section */}
                  <div className="relative rounded-lg overflow-hidden bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 p-4 sm:p-5 mb-4">
                    {/* Profile Header */}
                    <div className="flex items-center gap-4 mb-6">
                      <div className="relative">
                        <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center text-white text-xl font-bold shadow-lg overflow-hidden">
                          <img
                            src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80&w=200"
                            alt="John Doe"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 rounded-full bg-green-500 border-2 border-white flex items-center justify-center">
                          <Check className="w-2.5 h-2.5 text-white" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg sm:text-xl font-bold text-gray-900">
                          John Doe{" "}
                          <span className="text-xs font-normal text-gray-500">
                            @johndoe
                          </span>
                        </h3>
                        <p className="text-sm text-gray-600">
                          Aspiring Full Stack Developer
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <GraduationCap className="w-3.5 h-3.5" />
                            <span>Computer Science Graduate</span>
                          </div>
                          <div className="w-1 h-1 rounded-full bg-gray-300"></div>
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Target className="w-3.5 h-3.5 text-upskilleo-purple" />
                            <span>Seeking Opportunities</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Learning Journey */}
                    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
                      <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <BookOpen className="w-4 h-4 text-upskilleo-purple" />
                        Learning Journey
                      </h4>
                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center text-white text-xs font-medium">
                            <Check className="w-4 h-4" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-900">
                              Completed Full Stack Development Bootcamp
                            </p>
                            <p className="text-xs text-gray-500">
                              Mastered modern web technologies and best
                              practices
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center text-white text-xs font-medium">
                            <Award className="w-4 h-4" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-900">
                              Earned Industry-Recognized Certifications
                            </p>
                            <p className="text-xs text-gray-500">
                              React, Node.js, and AWS certifications
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center text-white text-xs font-medium">
                            <Users className="w-4 h-4" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-900">
                              Built Projects with Real-World Impact
                            </p>
                            <p className="text-xs text-gray-500">
                              Developed solutions for local businesses and
                              communities
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Skills Showcase */}
                    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
                      <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <Layers className="w-4 h-4 text-upskilleo-purple" />
                        Technical Skills
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center justify-between text-xs mb-1">
                            <span className="text-gray-600">
                              Frontend Development
                            </span>
                            <span className="text-upskilleo-purple">
                              Proficient
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1.5">
                            {[
                              "React",
                              "Next.js",
                              "TypeScript",
                              "Tailwind CSS",
                            ].map((skill) => (
                              <Badge
                                key={skill}
                                variant="outline"
                                className="text-xs bg-white/80 backdrop-blur-sm border-upskilleo-purple/20 text-upskilleo-purple"
                              >
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between text-xs mb-1">
                            <span className="text-gray-600">
                              Backend Development
                            </span>
                            <span className="text-upskilleo-purple">
                              Proficient
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1.5">
                            {["Node.js", "Express", "MongoDB", "REST APIs"].map(
                              (skill) => (
                                <Badge
                                  key={skill}
                                  variant="outline"
                                  className="text-xs bg-white/80 backdrop-blur-sm border-upskilleo-purple/20 text-upskilleo-purple"
                                >
                                  {skill}
                                </Badge>
                              )
                            )}
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between text-xs mb-1">
                            <span className="text-gray-600">
                              Tools & Practices
                            </span>
                            <span className="text-upskilleo-purple">
                              Familiar
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1.5">
                            {["Git", "Docker", "AWS", "CI/CD"].map((skill) => (
                              <Badge
                                key={skill}
                                variant="outline"
                                className="text-xs bg-white/80 backdrop-blur-sm border-upskilleo-purple/20 text-upskilleo-purple"
                              >
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Projects Showcase */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="text-base sm:text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <Code className="w-4 h-4 text-upskilleo-purple" />
                        Featured Projects
                      </h4>
                      <Badge
                        variant="outline"
                        className="text-xs bg-white/90 backdrop-blur-sm border-upskilleo-purple/20 text-upskilleo-purple font-medium px-2.5 py-0.5 hover:bg-white hover:border-upskilleo-purple/40 transition-colors"
                      >
                        6 Projects Completed
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 gap-3">
                      {[
                        {
                          title: "Community Marketplace",
                          description:
                            "Built a full-stack marketplace for local artisans to showcase and sell their products",
                          image:
                            "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?auto=format&fit=crop&q=80&w=800",
                          tags: ["React", "Node.js", "MongoDB"],
                          impact: "Helped 50+ local artisans go digital",
                          github: "150+ stars",
                        },
                        {
                          title: "Task Management App",
                          description:
                            "Developed a collaborative project management tool for student groups",
                          image:
                            "https://images.unsplash.com/photo-1540350394557-8d14678e7f91?auto=format&fit=crop&q=80&w=800",
                          tags: ["Next.js", "TypeScript", "PostgreSQL"],
                          impact: "Used by 5+ student organizations",
                          github: "100+ stars",
                        },
                      ].map((project) => (
                        <div
                          key={project.title}
                          className="group relative rounded-lg border border-gray-200 bg-white overflow-hidden hover:shadow-lg transition-all duration-300"
                        >
                          {/* Project Image */}
                          <div className="relative aspect-video overflow-hidden">
                            <img
                              src={project.image}
                              alt={project.title}
                              className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                            <div className="absolute bottom-0 left-0 right-0 p-3">
                              <div className="flex items-center justify-between text-white/90 text-xs">
                                <div className="flex items-center gap-2">
                                  <Github className="w-3.5 h-3.5" />
                                  <span>{project.github}</span>
                                </div>
                                <div className="flex items-center gap-1 bg-green-500/90 px-2 py-0.5 rounded-full">
                                  <Target className="w-3 h-3" />
                                  <span>{project.impact}</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Project Content */}
                          <div className="p-3">
                            <div className="flex items-start justify-between gap-2">
                              <div>
                                <h5 className="font-semibold text-gray-900 group-hover:text-upskilleo-purple transition-colors">
                                  {project.title}
                                </h5>
                                <p className="text-sm text-gray-600 mt-0.5 line-clamp-2">
                                  {project.description}
                                </p>
                              </div>
                            </div>

                            {/* Tags */}
                            <div className="flex flex-wrap gap-1.5 mt-2">
                              {project.tags.map((tag) => (
                                <Badge
                                  key={tag}
                                  variant="outline"
                                  className="text-xs bg-white/80 backdrop-blur-sm border-upskilleo-purple/20 text-upskilleo-purple"
                                >
                                  {tag}
                                </Badge>
                              ))}
                            </div>

                            {/* Hover Action */}
                            <div className="absolute inset-0 bg-gradient-to-t from-upskilleo-purple/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-end justify-center p-3">
                              <Button
                                variant="outline"
                                size="sm"
                                className="bg-white/90 backdrop-blur-sm border-upskilleo-purple/20 text-upskilleo-purple hover:bg-white hover:border-upskilleo-purple/40 transition-all transform translate-y-2 group-hover:translate-y-0"
                              >
                                View Project
                                <ArrowRight className="w-3.5 h-3.5 ml-1" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Call to Action */}
                    <div className="mt-4 p-4 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 border border-upskilleo-purple/10">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center">
                          <MessageSquare className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold text-gray-900">
                            Let's Connect!
                          </h4>
                          <p className="text-xs text-gray-600">
                            Open to opportunities and collaborations
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Features List */}
              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-50 border border-gray-100">
                  <div className="w-7 h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center shadow-sm">
                    <Check className="w-3.5 h-3.5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-gray-900">
                      Fully Customizable
                    </h4>
                    <p className="text-xs text-gray-600">
                      Personalize colors, layout, and content
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-50 border border-gray-100">
                  <div className="w-7 h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center shadow-sm">
                    <Globe className="w-3.5 h-3.5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-gray-900">
                      Responsive Design
                    </h4>
                    <p className="text-xs text-gray-600">
                      Looks great on all devices
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-50 border border-gray-100">
                  <div className="w-7 h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center shadow-sm">
                    <TrendingUp className="w-3.5 h-3.5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-gray-900">
                      SEO Optimized
                    </h4>
                    <p className="text-xs text-gray-600">
                      Get discovered by recruiters
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-50 border border-gray-100">
                  <div className="w-7 h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center shadow-sm">
                    <MessageSquare className="w-3.5 h-3.5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-gray-900">
                      Contact Form
                    </h4>
                    <p className="text-xs text-gray-600">
                      Let recruiters reach out easily
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Fixed CTA at bottom */}
          <div className="p-3 sm:p-4 border-t border-border bg-background">
            <Button
              className="w-full bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white py-3 text-sm font-semibold hover:opacity-90 active:scale-[0.98] transition-transform shadow-lg"
              onClick={handleEnroll}
            >
              Get Your Portfolio
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Certificate Preview Dialog */}
      <Dialog
        open={showCertificatePreview}
        onOpenChange={setShowCertificatePreview}
      >
        <DialogContent className="w-[95vw] max-w-[400px] bg-background/95 backdrop-blur-sm p-0 overflow-hidden rounded-xl sm:rounded-lg">
          {/* Hero Section */}
          <div className="relative p-4 sm:p-5 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple text-white">
            <div className="relative">
              <h2 className="text-lg sm:text-xl font-bold">
                Industry-Recognized Certificate
              </h2>
              <p className="text-sm text-white/90 mt-0.5">
                Your proof of expertise
              </p>
            </div>
          </div>

          <div className="p-4 sm:p-5 space-y-4">
            {/* Certificate Preview */}
            <div className="relative overflow-hidden rounded-lg border border-gray-200 bg-white p-4">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 rounded-full blur-2xl -translate-y-1/2 translate-x-1/2"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center">
                      <Award className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        Full Stack Development
                      </h3>
                      <p className="text-xs text-gray-500">
                        Professional Certificate
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-gray-500">Issued by</div>
                    <div className="font-medium text-gray-900">Upskilleo</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Check className="w-4 h-4 text-upskilleo-purple" />
                    <span className="text-gray-600">
                      Verified by industry experts
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Check className="w-4 h-4 text-upskilleo-purple" />
                    <span className="text-gray-600">
                      Recognized by 200+ companies
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Check className="w-4 h-4 text-upskilleo-purple" />
                    <span className="text-gray-600">
                      LinkedIn-verified credentials
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Trust Badge */}
            <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 p-2.5 cursor-pointer transition-all duration-300 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 active:scale-[0.98]">
              <div className="relative flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <Users className="w-4 h-4 text-upskilleo-purple group-hover:scale-110 transition-transform" />
                  <span className="text-xs sm:text-sm">
                    Trusted by Top Tech Companies
                  </span>
                </div>
                <ChevronRight className="w-4 h-4 text-upskilleo-purple/50 group-hover:translate-x-0.5 transition-transform" />
              </div>
            </div>

            {/* CTA */}
            <Button
              className="w-full bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white py-4 sm:py-5 text-sm sm:text-base font-semibold hover:opacity-90 active:scale-[0.98] transition-transform"
              onClick={handleEnroll}
            >
              Get Your Certificate
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Projects Preview Dialog */}
      <Dialog open={showProjectsPreview} onOpenChange={setShowProjectsPreview}>
        <DialogContent className="w-[95vw] max-w-[400px] bg-background/95 backdrop-blur-sm p-0 overflow-hidden rounded-xl sm:rounded-lg">
          {/* Hero Section */}
          <div className="relative p-4 sm:p-5 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple text-white">
            <div className="relative">
              <h2 className="text-lg sm:text-xl font-bold">
                Real-World Projects
              </h2>
              <p className="text-sm text-white/90 mt-0.5">
                Build what employers want to see
              </p>
            </div>
          </div>

          <div className="p-4 sm:p-5 space-y-4">
            {/* Project Types */}
            <div className="grid grid-cols-2 gap-2">
              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <Globe className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  Full-Stack Apps
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  E-commerce, social media
                </p>
              </div>

              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <Layers className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  APIs & Services
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  REST, GraphQL, Microservices
                </p>
              </div>

              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <Users className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  Team Projects
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  Real collaboration
                </p>
              </div>

              <div className="group p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 active:scale-[0.98] transition-transform">
                <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center mb-1.5 group-hover:scale-110 transition-transform">
                  <TrendingUp className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-medium text-xs sm:text-sm">
                  Production Ready
                </h4>
                <p className="text-[10px] sm:text-xs text-muted-foreground">
                  Deploy & scale
                </p>
              </div>
            </div>

            {/* Project Features */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 p-2 rounded-lg bg-white border border-gray-200 shadow-sm">
                <div className="w-6 h-6 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0">
                  <Check className="w-3.5 h-3.5 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">
                  Real-world problem solving
                </span>
              </div>
              <div className="flex items-center gap-2 p-2 rounded-lg bg-white border border-gray-200 shadow-sm">
                <div className="w-6 h-6 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0">
                  <Check className="w-3.5 h-3.5 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">
                  Industry best practices
                </span>
              </div>
              <div className="flex items-center gap-2 p-2 rounded-lg bg-white border border-gray-200 shadow-sm">
                <div className="w-6 h-6 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0">
                  <Check className="w-3.5 h-3.5 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">
                  Code review & feedback
                </span>
              </div>
            </div>

            {/* CTA */}
            <Button
              className="w-full bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white py-4 sm:py-5 text-sm sm:text-base font-semibold hover:opacity-90 active:scale-[0.98] transition-transform"
              onClick={handleEnroll}
            >
              Start Building Projects
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Badges Preview Dialog */}
      <Dialog open={showBadgesPreview} onOpenChange={setShowBadgesPreview}>
        <DialogContent className="w-[95vw] max-w-[400px] bg-background/95 backdrop-blur-sm p-0 overflow-hidden rounded-xl sm:rounded-lg">
          {/* Hero Section */}
          <div className="relative p-4 sm:p-5 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple text-white">
            <div className="relative">
              <h2 className="text-lg sm:text-xl font-bold">
                Verified Skill Badges
              </h2>
              <p className="text-sm text-white/90 mt-0.5">
                Showcase your expertise
              </p>
            </div>
          </div>

          <div className="p-4 sm:p-5 space-y-4">
            {/* Badges Grid */}
            <div className="grid grid-cols-2 gap-2">
              {[
                { name: "React", level: "Advanced", icon: "⚛️" },
                { name: "Node.js", level: "Proficient", icon: "🟢" },
                { name: "TypeScript", level: "Advanced", icon: "📘" },
                { name: "AWS", level: "Intermediate", icon: "☁️" },
                { name: "MongoDB", level: "Proficient", icon: "🍃" },
                { name: "GraphQL", level: "Intermediate", icon: "📊" },
              ].map((badge, i) => (
                <div
                  key={i}
                  className="group p-3 rounded-lg bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 rounded-full bg-white border border-gray-200 flex items-center justify-center text-lg shadow-sm">
                      {badge.icon}
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-gray-900">
                        {badge.name}
                      </h4>
                      <p className="text-xs font-medium text-upskilleo-purple">
                        {badge.level}
                      </p>
                    </div>
                  </div>
                  <div className="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple rounded-full"
                      style={{
                        width:
                          badge.level === "Advanced"
                            ? "90%"
                            : badge.level === "Proficient"
                            ? "75%"
                            : "60%",
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>

            {/* Badge Features */}
            <div className="space-y-2">
              <div className="flex items-center gap-3 p-3 rounded-lg bg-white border border-gray-200 shadow-sm">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0">
                  <Globe className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">
                  LinkedIn integration
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-lg bg-white border border-gray-200 shadow-sm">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0">
                  <Award className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">
                  Verified by industry experts
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-lg bg-white border border-gray-200 shadow-sm">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0">
                  <TrendingUp className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900">
                  Auto-updates with new skills
                </span>
              </div>
            </div>

            {/* CTA */}
            <Button
              className="w-full bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white py-4 sm:py-5 text-sm sm:text-base font-semibold hover:opacity-90 active:scale-[0.98] transition-transform"
              onClick={handleEnroll}
            >
              Earn Your Badges
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Enrollment Popup Dialog */}
      <Dialog open={showEnrollmentPopup} onOpenChange={setShowEnrollmentPopup}>
        <DialogContent className="w-[95vw] max-w-[500px] bg-background/95 backdrop-blur-sm p-0 overflow-hidden rounded-xl sm:rounded-lg">
          {/* Header */}
          <div className="relative p-4 sm:p-5 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple text-white">
            <div className="relative">
              <h2 className="text-lg sm:text-xl font-bold">
                Complete Your Enrollment
              </h2>
              <p className="text-sm text-white/90 mt-0.5">
                Get instant access to {course?.title}
              </p>
            </div>
          </div>

          <div className="p-4 sm:p-5 space-y-4">
            {/* Course Summary */}
            <div className="flex items-start gap-4 p-4 rounded-lg bg-upskilleo-soft-purple/10 border border-upskilleo-purple/20">
              <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={course?.image}
                  alt={course?.title}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg text-gray-900 mb-1">
                  {course?.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  {course?.description}
                </p>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1 text-gray-500">
                    <Clock className="w-4 h-4" />
                    <span>{course?.level}</span>
                  </div>
                  <div className="flex items-center gap-1 text-gray-500">
                    <Users className="w-4 h-4" />
                    <span>{course?.students?.toLocaleString()}+ students</span>
                  </div>
                </div>
              </div>
            </div>

            {/* What's Included */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900">What's included:</h4>
              <div className="grid grid-cols-1 gap-2">
                {course?.features?.slice(0, 4).map((feature, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-2 p-2 rounded-lg bg-gray-50"
                  >
                    <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center justify-between p-4 rounded-lg bg-gradient-to-r from-upskilleo-purple/5 to-upskilleo-deep-purple/5 border border-upskilleo-purple/10">
              <div>
                <p className="text-sm text-gray-600">Total Price</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${course?.price}
                </p>
              </div>
              <Badge className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1.5 text-xs font-bold">
                <div className="flex items-center gap-1.5">
                  <TrendingUp className="w-3 h-3" />
                  SAVE 50%
                </div>
              </Badge>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              <Button
                onClick={handleBuyNow}
                disabled={enrolling}
                className="flex-1 bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white py-3 text-base font-semibold hover:opacity-90 disabled:opacity-50 transition-all"
              >
                {enrolling ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Enrolling...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Check className="w-4 h-4" />
                    Buy Now & Get Instant Access
                  </div>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowEnrollmentPopup(false)}
                className="border-upskilleo-purple text-upskilleo-purple hover:bg-upskilleo-soft-purple"
              >
                Cancel
              </Button>
            </div>

            {/* Security Note */}
            <div className="text-center">
              <p className="text-xs text-gray-500">
                🔒 Secure payment • 30-day money-back guarantee
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Footer />

      {/* Remove the animation styles and script */}
      <style jsx global>{`
        /* Remove the timeline animation styles */
      `}</style>

      {/* Remove the animation script */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
          // Remove the animation observer
          `,
        }}
      />
    </div>
  );
};

export default CourseDetail;
