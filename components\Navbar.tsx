"use client";

import React from "react";
import { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { UserCircle, LogIn, BookOpen, GraduationCap } from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import userService from "@/services/userService";
import apiService from "@/services/apiService";

interface User {
  id: string;
  name: string;
  email?: string;
  enrolledCoursesCount?: number;
  enrolledCourseIds?: string[];
}

const Navbar = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [enrolledCoursesCount, setEnrolledCoursesCount] = useState<number>(0);
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    // Always apply dark mode
    document.documentElement.classList.add("dark");
    localStorage.setItem("upskilleo-theme", "dark");

    // Use userService to check authentication - no longer need URL params
    const hasValidAuth = userService.hasValidAuth();
    const userData = userService.getUserData();

    if (hasValidAuth && userData) {
      setUser(userData);
      setIsLoggedIn(true);
    } else {
      // Clear invalid auth data
      userService.clearUserData();
      setUser(null);
      setIsLoggedIn(false);
    }

    // Set loading to false after determining the state
    setIsLoading(false);
  }, []); // Remove searchParams dependency since we don't need URL params anymore

  // Fetch enrolled courses count from API (not local storage)
  useEffect(() => {
    const fetchEnrollments = async () => {
      if (isLoggedIn) {
        try {
          const res = await apiService.getUserEnrollments();
          setEnrolledCoursesCount(res.enrolledCoursesCount || 0);
        } catch (e) {
          setEnrolledCoursesCount(0);
        }
      } else {
        setEnrolledCoursesCount(0);
      }
    };
    fetchEnrollments();
  }, [isLoggedIn]);

  const handleLogin = () => {
    // Clear any existing invalid auth data
    localStorage.removeItem("upskilleo-user");
    localStorage.removeItem("token");
    setUser(null);
    setIsLoggedIn(false);

    router.push(`/auth?redirect=${encodeURIComponent(pathname)}`);
  };

  const handleLogout = () => {
    // Logout and redirect to current page
    localStorage.removeItem("upskilleo-user");
    setUser(null);
    setIsLoggedIn(false);

    router.push(pathname);

    toast.success("Logged out successfully", {
      description: "See you next time!",
    });
  };

  return (
    <nav className="sticky top-0 z-50 w-full py-4 glass-morphism backdrop-blur-md bg-background/80 border-b border-border/40">
      <div className="container mx-auto flex items-center justify-between">
        <Link href="/" className="flex items-center">
          <span className="text-2xl font-bold text-gradient">Upskilleo</span>
        </Link>

        <div className="flex items-center gap-4">
          {isLoading ? (
            <div className="flex items-center gap-4">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-24" />
            </div>
          ) : isLoggedIn ? (
            <>
              <Link
                href="/learning"
                className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
              >
                <BookOpen size={16} />
                Your Learning
                {enrolledCoursesCount > 0 && (
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {enrolledCoursesCount}
                  </Badge>
                )}
              </Link>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <GraduationCap size={14} />
                  <span>{enrolledCoursesCount} courses</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLogout}
                  className="flex items-center gap-2"
                >
                  <UserCircle size={18} />
                  {user?.name ? `Logout (${user.name})` : "Logout"}
                </Button>
              </div>
            </>
          ) : (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogin}
                className="flex items-center gap-2"
              >
                <LogIn size={18} />
                Login
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={() =>
                  router.push(
                    `/auth?tab=signup&redirect=${encodeURIComponent(pathname)}`
                  )
                }
              >
                Sign Up
              </Button>
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
