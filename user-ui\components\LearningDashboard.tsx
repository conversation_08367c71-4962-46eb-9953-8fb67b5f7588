"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BookOpen,
  Clock,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  PlayCircle,
  Award,
  Calendar,
  Zap,
  ArrowRight,
  Loader2,
  Target,
  TrendingUp,
} from "lucide-react";
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import apiService from "@/services/apiService";
import userService from "@/services/userService";
import { toast } from "@/hooks/use-toast";

interface EnrolledCourse {
  id: string;
  slug: string;
  title: string;
  description: string;
  longDescription: string;
  image: string;
  level: string;
  instructor: string;
  rating: number;
  reviews: number;
  students: number;
  lastUpdated: string;
  price: number;
  progress: number;
  completedLessons: number;
  totalLessons: number;
  enrolledAt: string;
  lastAccessed: string;
  nextLesson: string;
  duration: string;
  lessons: number;
  modules: number;
  isCompleted: boolean;
  isInProgress: boolean;
  isNotStarted: boolean;
  features: string[];
  topics: string[];
  badge?: string;
  color?: string;
}

interface UserData {
  id: string;
  name: string;
  enrolledCoursesCount?: number;
  enrolledCourseIds?: string[];
}

interface LearningStats {
  totalCourses: number;
  completedCourses: number;
  inProgressCourses: number;
  averageProgress: number;
  totalMinutesLearned: number;
}

const LearningDashboard = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [enrolledCourses, setEnrolledCourses] = useState<EnrolledCourse[]>([]);
  const [learningStats, setLearningStats] = useState<LearningStats>({
    totalCourses: 0,
    completedCourses: 0,
    inProgressCourses: 0,
    averageProgress: 0,
    totalMinutesLearned: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddingSamples, setIsAddingSamples] = useState(false);

  const expandedParam = searchParams.get("expanded");
  const initialExpandedCourseIds = expandedParam
    ? expandedParam.split(",")
    : [];

  const [expandedCourseIds, setExpandedCourseIds] = useState<string[]>(
    initialExpandedCourseIds
  );

  const fetchEnrolledCourses = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check if user is authenticated
      if (!userService.hasValidAuth()) {
        setError("User not authenticated");
        setIsLoading(false);
        return;
      }

      // Fetch enrolled courses from API
      const response = await apiService.getEnrolledCourses();

      if (response.success) {
        setUserData(response.data.user);
        setEnrolledCourses(response.data.enrolledCourses);
        setLearningStats(response.data.statistics);
      } else {
        setError("Failed to fetch enrolled courses");
      }
    } catch (err) {
      console.error("Error fetching enrolled courses:", err);
      setError("Failed to load your enrolled courses");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchEnrolledCourses();
  }, []);

  const addSampleEnrollments = async () => {
    try {
      setIsAddingSamples(true);
      await apiService.addSampleEnrollments();
      toast({
        title: "Sample enrollments added!",
        description: "You now have 2 courses enrolled for testing.",
      });
      fetchEnrolledCourses(); // Refresh the data
    } catch (err) {
      console.error("Error adding sample enrollments:", err);
      toast({
        title: "Error",
        description: "Failed to add sample enrollments.",
        variant: "destructive",
      });
    } finally {
      setIsAddingSamples(false);
    }
  };

  const updateCourseProgress = async (
    courseId: string,
    newProgress: number
  ) => {
    try {
      const completedLessons = Math.floor((newProgress / 100) * 12); // Assuming 12 total lessons
      await apiService.updateCourseProgress(
        courseId,
        newProgress,
        completedLessons
      );
      toast({
        title: "Progress updated!",
        description: `Course progress updated to ${newProgress}%`,
      });
      fetchEnrolledCourses(); // Refresh the data
    } catch (err) {
      console.error("Error updating progress:", err);
      toast({
        title: "Error",
        description: "Failed to update course progress.",
        variant: "destructive",
      });
    }
  };

  const removeEnrollment = async (courseId: string) => {
    try {
      await apiService.removeEnrollment(courseId);
      toast({
        title: "Enrollment removed!",
        description: "Course has been removed from your enrollments.",
      });
      fetchEnrolledCourses(); // Refresh the data
    } catch (err) {
      console.error("Error removing enrollment:", err);
      toast({
        title: "Error",
        description: "Failed to remove enrollment.",
        variant: "destructive",
      });
    }
  };

  const toggleCourseExpansion = (courseId: string) => {
    setExpandedCourseIds((prev) => {
      const newState = prev.includes(courseId)
        ? prev.filter((id) => id !== courseId)
        : [...prev, courseId];

      if (newState.length > 0) {
        const newParams = new URLSearchParams(searchParams.toString());
        newParams.set("expanded", newState.join(","));
        router.push(`?${newParams.toString()}`);
      } else {
        const newParams = new URLSearchParams(searchParams.toString());
        newParams.delete("expanded");
        router.push(`?${newParams.toString()}`);
      }

      return newState;
    });
  };

  const formatLastAccessed = (lastAccessed: string) => {
    const date = new Date(lastAccessed);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return "Today";
    } else if (diffDays === 1) {
      return "Yesterday";
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7);
      return `${weeks} week${weeks > 1 ? "s" : ""} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const renderCourseCard = (
    course: EnrolledCourse,
    isCompleted: boolean = false
  ) => (
    <Card
      key={course.id}
      className="overflow-hidden border-border hover:border-primary/50 transition-all duration-300 group"
    >
      <div className="relative h-48 overflow-hidden">
        <img
          src={course.image}
          alt={course.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-background/90 via-background/50 to-transparent"></div>

        {/* Progress overlay */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="flex items-center justify-between text-white mb-2">
            <span className="text-sm font-medium">
              {isCompleted ? "Completed!" : `${course.progress}% Complete`}
            </span>
            <Badge variant="secondary" className="text-xs">
              {course.level}
            </Badge>
          </div>
          <Progress value={course.progress} className="h-2 bg-background/40" />
        </div>

        {/* Status badge */}
        {isCompleted && (
          <div className="absolute top-4 right-4">
            <Badge className="bg-green-500 text-white">
              <CheckCircle className="w-3 h-3 mr-1" />
              Complete
            </Badge>
          </div>
        )}

        {/* Course badge */}
        {course.badge && (
          <div className="absolute top-4 left-4">
            <Badge className="bg-upskilleo-purple text-white">
              {course.badge}
            </Badge>
          </div>
        )}
      </div>

      <div className="p-6">
        <div className="flex justify-between items-start mb-3">
          <h3 className="font-semibold text-lg leading-tight">
            {course.title}
          </h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleCourseExpansion(course.id)}
            className="p-1 h-auto text-muted-foreground hover:text-primary"
            aria-label="Toggle course details"
          >
            {expandedCourseIds.includes(course.id) ? (
              <ChevronDown size={20} />
            ) : (
              <ChevronRight size={20} />
            )}
          </Button>
        </div>

        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
          <div className="flex items-center gap-1">
            <Clock size={14} />
            <span>{course.duration}</span>
          </div>
          <div className="flex items-center gap-1">
            <BookOpen size={14} />
            <span>{course.lessons} lessons</span>
          </div>
        </div>

        <p className="text-sm text-muted-foreground mb-4">
          {isCompleted ? "Status: Complete" : `Next: ${course.nextLesson}`}
        </p>

        <div className="flex items-center justify-between">
          <Button
            variant="default"
            size="sm"
            asChild
            className={`flex-1 mr-2${
              !isCompleted
                ? " bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white shadow-md hover:scale-105 border-none"
                : ""
            }`}
          >
            <Link
              href={`/module/${course.id}`}
              className="flex items-center gap-2"
            >
              {isCompleted ? (
                <>
                  <Award size={16} className="text-yellow-500" />
                  Review
                </>
              ) : (
                <>
                  <PlayCircle size={16} />
                  Continue
                </>
              )}
            </Link>
          </Button>

          <Button variant="outline" size="sm" asChild>
            <Link
              href={`/course-detail/${course.id}`}
              className="flex items-center gap-2"
            >
              <ArrowRight size={14} />
              Details
            </Link>
          </Button>
        </div>

        <Collapsible open={expandedCourseIds.includes(course.id)}>
          <CollapsibleContent className="mt-4 pt-4 border-t border-border/50 animate-fade-in">
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Last accessed:</span>
                  <p className="font-medium">
                    {formatLastAccessed(course.lastAccessed)}
                  </p>
                </div>
                <div>
                  <span className="text-muted-foreground">
                    Completed lessons:
                  </span>
                  <p className="font-medium">
                    {course.completedLessons}/{course.totalLessons}
                  </p>
                </div>
                {isCompleted && (
                  <>
                    <div>
                      <span className="text-muted-foreground">Completed:</span>
                      <p className="font-medium">
                        {new Date(course.enrolledAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Rating:</span>
                      <p className="font-medium text-green-500">
                        {course.rating}/5
                      </p>
                    </div>
                  </>
                )}
              </div>

              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href={`/course-detail/${course.id}`}>
                  {isCompleted
                    ? "Download Certificate"
                    : "Full Course Overview"}
                </Link>
              </Button>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </Card>
  );

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto max-w-6xl">
        <div className="text-center py-20">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-upskilleo-purple" />
          <h2 className="text-xl font-semibold mb-2">
            Loading your courses...
          </h2>
          <p className="text-muted-foreground">
            Fetching your enrolled courses and progress data.
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto max-w-4xl text-center py-20">
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-red-100 flex items-center justify-center">
            <BookOpen className="w-12 h-12 text-red-500" />
          </div>
          <h2 className="text-2xl font-bold mb-4">Error Loading Courses</h2>
          <p className="text-muted-foreground mb-8">{error}</p>
          <Button onClick={() => window.location.reload()} size="lg">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // If no enrolled courses, show empty state
  if (enrolledCourses.length === 0) {
    return (
      <div className="container mx-auto max-w-4xl text-center py-20">
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-upskilleo-purple/20 to-upskilleo-deep-purple/20 flex items-center justify-center">
            <BookOpen className="w-12 h-12 text-upskilleo-purple" />
          </div>
          <h2 className="text-2xl font-bold mb-4">No Enrolled Courses Yet</h2>
          <p className="text-muted-foreground mb-8">
            You haven&apos;t enrolled in any courses yet. Start your learning
            journey by exploring our course catalog.
          </p>
          <Button asChild size="lg">
            <Link href="/">Browse Courses</Link>
          </Button>
        </div>
      </div>
    );
  }

  const inProgressCourses = enrolledCourses.filter(
    (course) => course.isInProgress
  );
  const completedCourses = enrolledCourses.filter(
    (course) => course.isCompleted
  );
  const notStartedCourses = enrolledCourses.filter(
    (course) => course.isNotStarted
  );

  return (
    <div className="container mx-auto max-w-6xl">
      {/* Header */}
      <div className="text-center mb-12 animate-fade-in">
        <h1 className="text-3xl sm:text-4xl font-bold mb-4">
          Your{" "}
          <span className="bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach bg-clip-text text-transparent">
            Learning
          </span>{" "}
          Dashboard
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Welcome back, {userData?.name}! Continue your learning journey where
          you left off.
        </p>
      </div>

      {/* Dynamic Enrollment Testing Section */}
      {enrolledCourses.length === 0 && (
        <Card className="mb-8 border-dashed border-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Dynamic Enrollment Testing
            </CardTitle>
            <CardDescription>
              Add sample enrollments to test the learning dashboard
              functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={addSampleEnrollments}
              disabled={isAddingSamples}
              className="w-full sm:w-auto"
            >
              {isAddingSamples ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Adding Sample Enrollments...
                </>
              ) : (
                <>
                  <BookOpen className="h-4 w-4 mr-2" />
                  Add Sample Enrollments (2 Courses)
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Learning Stats */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-12">
        <div className="bg-card border border-border rounded-xl p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <BookOpen className="h-5 w-5 text-upskilleo-purple mr-2" />
            <span className="text-2xl font-bold">
              {learningStats.totalCourses}
            </span>
          </div>
          <p className="text-xs text-muted-foreground">Enrolled Courses</p>
        </div>

        <div className="bg-card border border-border rounded-xl p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Target className="h-5 w-5 text-blue-500 mr-2" />
            <span className="text-2xl font-bold">
              {learningStats.averageProgress}%
            </span>
          </div>
          <p className="text-xs text-muted-foreground">Avg Progress</p>
        </div>

        <div className="bg-card border border-border rounded-xl p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <TrendingUp className="h-5 w-5 text-orange-500 mr-2" />
            <span className="text-2xl font-bold">
              {learningStats.inProgressCourses}
            </span>
          </div>
          <p className="text-xs text-muted-foreground">In Progress</p>
        </div>

        <div className="bg-card border border-border rounded-xl p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            <span className="text-2xl font-bold">
              {learningStats.completedCourses}
            </span>
          </div>
          <p className="text-xs text-muted-foreground">Completed</p>
        </div>

        <div className="bg-card border border-border rounded-xl p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Zap className="h-5 w-5 text-yellow-500 mr-2" />
            <span className="text-2xl font-bold">
              {learningStats.totalMinutesLearned}
            </span>
          </div>
          <p className="text-xs text-muted-foreground">Minutes Learned</p>
        </div>
      </div>

      {/* Continue Learning Section */}
      {inProgressCourses.length > 0 && (
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <PlayCircle className="w-6 h-6 text-upskilleo-purple" />
              Continue Learning
            </h2>
            <Badge variant="secondary" className="text-sm">
              {inProgressCourses.length} active
            </Badge>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {inProgressCourses.map((course) => renderCourseCard(course))}
          </div>
        </section>
      )}

      {/* Completed Courses Section */}
      {completedCourses.length > 0 && (
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Award className="w-6 h-6 text-green-500" />
              Completed Courses
            </h2>
            <Badge
              variant="secondary"
              className="text-sm bg-green-500/10 text-green-600"
            >
              {completedCourses.length} completed
            </Badge>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {completedCourses.map((course) => renderCourseCard(course, true))}
          </div>
        </section>
      )}

      {/* Not Started Courses Section */}
      {notStartedCourses.length > 0 && (
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Calendar className="w-6 h-6 text-blue-500" />
              Ready to Start
            </h2>
            <Badge variant="secondary" className="text-sm">
              {notStartedCourses.length} courses
            </Badge>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {notStartedCourses.map((course) => renderCourseCard(course))}
          </div>
        </section>
      )}
    </div>
  );
};

export default LearningDashboard;
