/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import React, { useState, useEffect, useRef } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Play,
  ChevronRight,
  Video,
  BookOpen,
  Clock,
  Award,
  BarChart,
  Maximize2,
  Minimize2,
  Pause,
  SkipForward,
  SkipBack,
  Volume2,
  VolumeX,
  Check,
  Lock,
  Code,
  Lightbulb,
  X,
  ArrowRight,
  ArrowLeft,
  Trophy,
  Sparkles,
  List,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import CourseVideoPlayer, { CourseVideoPlayerHandle } from "@/components/CourseVideoPlayer";
import CourseEditor from "@/components/CourseEditor";
import CourseFeedback from "@/components/CourseFeedback";
import CourseModuleTimeline from "@/components/CourseModuleTimeline";
import { CourseModule } from "@/types/course";
import { toast } from "sonner";
import progressService from "@/services/progressService";
import userService from "@/services/userService";
import Link from "next/link";
import {
  DEFAULT_HTML_CODE,
  DEFAULT_CSS_CODE,
  DEFAULT_JS_CODE,
} from "@/constants/defaultCode";
import { useFullscreen } from "./useFullscreen";
import { AnimatePresence, motion } from "framer-motion";

interface KeyMoment {
  id: string;
  timeInSeconds: number;
  challenge: string;
  hints: string[];
  solution: string;
}

interface CourseSectionDetailProps {
  title: string;
  description?: string;
  modules: CourseModule[];
  courseId?: string;
  courseName?: string;
  moduleNumber?: number;
}

const CourseSectionDetail: React.FC<CourseSectionDetailProps> = ({
  title,
  description,
  modules,
  courseId,
  courseName,
  moduleNumber,
}) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const lessonIdParam = searchParams.get("lessonId");
const [currentModuleIndex, setCurrentModuleIndex] = useState(0);
const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
const [currentLessonIndex, setCurrentLessonIndex] = useState(0);
const [hasSetInitialLesson, setHasSetInitialLesson] = useState(false);

// Set initial lesson if lessonId is present in query, only once
useEffect(() => {
  if (!hasSetInitialLesson && lessonIdParam && modules && modules.length > 0) {
    let found = false;
    modules.forEach((mod, modIdx) => {
      mod.sections?.forEach((sec, secIdx) => {
        sec.lessons?.forEach((les, lesIdx) => {
          if (les.id === lessonIdParam) {
            setCurrentModuleIndex(modIdx);
            setCurrentSectionIndex(secIdx);
            setCurrentLessonIndex(lesIdx);
            found = true;
          }
        });
      });
    });
    setHasSetInitialLesson(true);
    // If not found, fallback to default (first lesson)
    if (!found) {
      setCurrentModuleIndex(0);
      setCurrentSectionIndex(0);
      setCurrentLessonIndex(0);
    }
  }
}, [lessonIdParam, modules, hasSetInitialLesson]);
  const [showEditor, setShowEditor] = useState(false);
  const [resumeVideo, setResumeVideo] = useState(false);
  const [code, setCode] = useState(DEFAULT_HTML_CODE);
  const [htmlCode, setHtmlCode] = useState("");
  const [cssCode, setCssCode] = useState("");
  const [jsCode, setJsCode] = useState("");
  const [showFeedback, setShowFeedback] = useState(false);
  const [currentKeyMoment, setCurrentKeyMoment] = useState<KeyMoment | null>(
    null
  );
  const [progress, setProgress] = useState(0);
  const [localModules, setLocalModules] = useState(modules);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [editorMode, setEditorMode] = useState<"web">("web");
  const [userData, setUserData] = useState<{ id: string; name: string } | null>(
    null
  );
  const [lastPausedTime, setLastPausedTime] = useState<number | null>(null);
  const [completedKeyMoments, setCompletedKeyMoments] = useState<string[]>([]);
  const [skippedKeyMoments, setSkippedKeyMoments] = useState<string[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const videoPlayerRef = useRef<CourseVideoPlayerHandle>(null);
  const transitionAudioRef = useRef<HTMLAudioElement | null>(null);

  const currentVideoKey = useRef(
    `module-${currentModuleIndex}-section-${currentSectionIndex}`
  );

  useEffect(() => {
    // Get user data from user service
    const userData = userService.getUserData();
    if (userData) {
      setUserData(userData);

      // Apply existing progress from localStorage
      const updatedModules = [...modules].map((module) => {
        const totalSections = module.sections?.length || 0;
        const moduleProgress = progressService.calculateModuleProgress(
          userData.id,
          "course-key", // This should be the actual course ID, using placeholder
          module.id,
          totalSections
        );

        // Update sections with completion status
        const updatedSections =
          module.sections?.map((section) => {
            const isCompleted = progressService.isSectionCompleted(
              userData.id,
              "course-key", // This should be the actual course ID, using placeholder
              module.id,
              section.id
            );

            // Deep clone lessons and only add/override completed, preserve all other fields (including status)
            const updatedLessons = (section.lessons || []).map((lesson) => ({
              ...lesson,
              // Optionally, you can add/override completed here if needed
              // completed: ...
            }));

            return {
              ...section,
              completed: isCompleted,
              lessons: updatedLessons,
            };
          }) || [];

        return {
          ...module,
          progress: moduleProgress,
          completed: moduleProgress === 100,
          sections: updatedSections,
        };
      });

      setLocalModules(updatedModules);
      calculateProgress(updatedModules);
    }
  }, [modules]);

  

  useEffect(() => {
    currentVideoKey.current = `module-${currentModuleIndex}-section-${currentSectionIndex}`;
  }, [currentModuleIndex, currentSectionIndex]);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    window.addEventListener("keydown", handleEscKey);
    return () => window.removeEventListener("keydown", handleEscKey);
  }, [isFullscreen]);

  // Auto-play first lesson of first section on mount or when modules change (only if no lessonId in query and initial lesson not set)
useEffect(() => {
  if (!hasSetInitialLesson && !lessonIdParam && modules && modules.length > 0) {
    const firstModule = modules[0];
    if (firstModule.sections && firstModule.sections.length > 0) {
      const firstSection = firstModule.sections[0];
      if (firstSection.lessons && firstSection.lessons.length > 0) {
        setCurrentModuleIndex(0);
        setCurrentSectionIndex(0);
        setCurrentLessonIndex(0);
      }
    }
    setHasSetInitialLesson(true);
  }
}, [hasSetInitialLesson, lessonIdParam, modules]);

  useEffect(() => {
    if (transitionAudioRef.current) {
      transitionAudioRef.current.currentTime = 0;
      transitionAudioRef.current.play();
    }
  }, [showEditor]);

  const calculateProgress = (updatedModules: CourseModule[]) => {
    let totalSections = 0;
    let completedSections = 0;

    updatedModules.forEach((module) => {
      if (module.sections) {
        totalSections += module.sections.length;
        completedSections += module.sections.filter(
          (section) => section.completed
        ).length;
      }
    });

    const calculatedProgress =
      totalSections > 0 ? (completedSections / totalSections) * 100 : 0;
    setProgress(calculatedProgress);
  };

  const handleKeyMomentEncountered = (keyMoment: KeyMoment) => {
    if (videoPlayerRef.current) {
      setLastPausedTime(videoPlayerRef.current.getCurrentTime());
    }
    setCurrentKeyMoment(keyMoment);
    setShowEditor(true);
    setIsPlaying(false);

    // Find the actual breakpoint data from the current lesson
    const currentLesson =
      localModules[currentModuleIndex]?.sections?.[currentSectionIndex]
        ?.lessons?.[currentLessonIndex];
    const breakpoint = currentLesson?.breakpoints?.find(
      (bp) => bp.id === keyMoment.id
    );

    // Extract boilerplate code from breakpoint's editorSettings
    let htmlBoilerplate = "";
    let cssBoilerplate = "";
    let jsBoilerplate = "";

    if (breakpoint?.editorSettings) {
      breakpoint.editorSettings.forEach((setting) => {
        switch (setting.type) {
          case "html":
            htmlBoilerplate = setting.boilerplateCode || "";
            break;
          case "css":
            cssBoilerplate = setting.boilerplateCode || "";
            break;
          case "js":
            jsBoilerplate = setting.boilerplateCode || "";
            break;
        }
      });
    }

    // Set the individual file codes with defaults if empty
    setHtmlCode(htmlBoilerplate || DEFAULT_HTML_CODE);
    setCssCode(cssBoilerplate || DEFAULT_CSS_CODE);
    setJsCode(jsBoilerplate || DEFAULT_JS_CODE);

    // Set the main code based on priority: HTML > CSS > JS > default
    let mainCode = "";

    if (htmlBoilerplate) {
      mainCode = htmlBoilerplate;
    } else if (cssBoilerplate) {
      mainCode = cssBoilerplate;
    } else if (jsBoilerplate) {
      mainCode = jsBoilerplate;
    } else {
      // If no boilerplate provided, use HTML default
      mainCode = DEFAULT_HTML_CODE;
    }

    setCode(mainCode);
    setResumeVideo(false);
  };

  // Function to determine the current editor language from breakpoint data
  const getCurrentEditorLanguage = () => {
    if (!currentKeyMoment) return "html";

    const currentLesson =
      localModules[currentModuleIndex]?.sections?.[currentSectionIndex]
        ?.lessons?.[currentLessonIndex];
    const breakpoint = currentLesson?.breakpoints?.find(
      (bp) => bp.id === currentKeyMoment.id
    );

    if (breakpoint?.editorSettings) {
      // Check for HTML first, then CSS, then JS
      if (
        breakpoint.editorSettings.some(
          (setting) => setting.type === "html" && setting.boilerplateCode
        )
      ) {
        return "html";
      } else if (
        breakpoint.editorSettings.some(
          (setting) => setting.type === "css" && setting.boilerplateCode
        )
      ) {
        return "css";
      } else if (
        breakpoint.editorSettings.some(
          (setting) => setting.type === "js" && setting.boilerplateCode
        )
      ) {
        return "javascript";
      }
    }

    return "html";
  };

  const handleSelectLesson = (
    moduleIndex: number,
    sectionIndex: number,
    lessonIndex: number
  ) => {
    setCurrentModuleIndex(moduleIndex);
    setCurrentSectionIndex(sectionIndex);
    setCurrentLessonIndex(lessonIndex);
    setShowEditor(false);
    setResumeVideo(false);
    setIsFullscreen(false);
    setHtmlCode("");
    setCssCode("");
    setJsCode("");
    setCode(DEFAULT_HTML_CODE);
    setLastPausedTime(null);
    setCompletedKeyMoments([]);
    setSkippedKeyMoments([]);
    setIsPlaying(false);
    calculateProgress(localModules);
  };

  const handleSubmitCode = () => {
    setShowFeedback(true);
    const isCorrect = code.includes(
      currentKeyMoment?.id === "1"
        ? "return num % 2 === 0"
        : currentKeyMoment?.id === "2"
        ? "return str.charAt(0).toUpperCase() + str.slice(1)"
        : "return numbers.filter(num => num > 0)"
    );

    if (isCorrect) {
      toast.success("Great job! Your solution is correct!");
      setTimeout(() => {
        handleContinue();
      }, 5000);
      // Mark as submitted, not skipped
      if (currentKeyMoment) {
        setCompletedKeyMoments((prev) => [...prev, currentKeyMoment.id]);
        setSkippedKeyMoments((prev) => prev.filter(id => id !== currentKeyMoment.id));
      }
    } else {
      toast.info("Try again! Your solution needs some work.");
    }
  };

  const handleContinue = () => {
    setShowFeedback(false);
    setShowEditor(false);
    setResumeVideo(true);
    setIsPlaying(true);
    // Mark the current key moment as completed
    if (currentKeyMoment) {
      setCompletedKeyMoments((prev) => [...prev, currentKeyMoment.id]);
    }
    const updatedModules = [...localModules];
    if (
      updatedModules[currentModuleIndex] &&
      updatedModules[currentModuleIndex].sections &&
      updatedModules[currentModuleIndex].sections[currentSectionIndex]
    ) {
      updatedModules[currentModuleIndex].sections[
        currentSectionIndex
      ].completed = true;
      setLocalModules(updatedModules);
      calculateProgress(updatedModules);
    }
  };

  const handleContinueVideo = () => {
    setShowEditor(false);
    setResumeVideo(true);
    setIsPlaying(true);
    // Mark the current key moment as completed
    if (currentKeyMoment) {
      setCompletedKeyMoments((prev) => [...prev, currentKeyMoment.id]);
    }
  };

  const handleSkipChallenge = () => {
    toast.info("Challenge skipped. Moving on to the next part.");
    setShowEditor(false);
    setResumeVideo(true);
    setIsPlaying(true);
    // Mark as skipped, not submitted
    if (currentKeyMoment) {
      setSkippedKeyMoments((prev) => [...prev, currentKeyMoment.id]);
      setCompletedKeyMoments((prev) => prev.filter(id => id !== currentKeyMoment.id));
    }
  };

  const handleVideoComplete = () => {
    toast.success("Lesson completed!", {
      description: "Moving to the next lesson automatically in 3 seconds",
    });

    const updatedModules = [...localModules];

    if (
      updatedModules[currentModuleIndex] &&
      updatedModules[currentModuleIndex].sections &&
      updatedModules[currentModuleIndex].sections[currentSectionIndex]
    ) {
      // Mark current section as completed
      updatedModules[currentModuleIndex].sections[
        currentSectionIndex
      ].completed = true;

      // Do NOT unlock next section locally; always trust backend locked state

      // Update progress in localStorage if user is logged in
      if (userData) {
        const currentModule = updatedModules[currentModuleIndex];
        const currentSection = currentModule?.sections?.[currentSectionIndex];

        if (currentSection) {
          progressService.updateSectionCompletion(
            userData.id,
            "course-key", // This should be the actual course ID, using a placeholder
            currentModule.id,
            currentSection.id,
            true
          );
        }
      }

      setLocalModules(updatedModules);
      calculateProgress(updatedModules);

      // Auto-advance to next section after delay
      setTimeout(() => {
        const currentModule = updatedModules[currentModuleIndex];
        const nextModule = updatedModules[currentModuleIndex + 1];

        if (
          currentModule?.sections &&
          currentSectionIndex < currentModule.sections.length - 1
        ) {
          setCurrentSectionIndex(currentSectionIndex + 1);
        } else if (nextModule?.sections && nextModule.sections.length > 0) {
          setCurrentModuleIndex(currentModuleIndex + 1);
          setCurrentSectionIndex(0);
        }
        setIsFullscreen(false);
      }, 3000);
    }
  };

  // Fullscreen API integration
  const {
    elementRef: fullscreenRef,
    enterFullscreen,
    exitFullscreen,
  } = useFullscreen<HTMLDivElement>((fs) => setIsFullscreen(fs));

  // Update toggleFullscreen to use the browser API
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      enterFullscreen();
    } else {
      exitFullscreen();
    }
  };

  const handleTogglePlay = () => {
    setIsPlaying((prev) => !prev);
  };

  // Handler for back to video from editor fullscreen
  const handleBackToVideo = () => {
    setShowEditor(false);
    setResumeVideo(true);
    setIsPlaying(true);
    if (currentKeyMoment) {
      setCompletedKeyMoments((prev) => [...prev, currentKeyMoment.id]);
    }
    // Do NOT exitFullscreen here; keep fullscreen if already in it
  };

  // Get current module, section, and lesson safely
  const currentModule = localModules[currentModuleIndex] || {
    title: "",
    sections: [],
    description: "",
  };
  const currentSection =
    currentModule.sections && currentModule.sections[currentSectionIndex]
      ? currentModule.sections[currentSectionIndex]
      : { title: "Introduction", duration: "0:00", videoUrl: "", lessons: [] };
  const currentLesson =
    currentSection.lessons && currentSection.lessons[currentLessonIndex]
      ? currentSection.lessons[currentLessonIndex]
      : null;

  // Map lesson breakpoints to keyMoments for the video player
  const keyMoments = (currentLesson?.breakpoints || []).map((bp) => ({
    id: bp.id,
    timeInSeconds: bp.openAtDuration ?? 0,
    challenge: bp.challengeType
      ? `Solve the ${bp.challengeType} challenge!`
      : "Solve the challenge!",
    hints: bp.editorSettings?.map((es) =>
      es.boilerplateCode
        ? `Start with: ${es.boilerplateCode}`
        : "Try your best!"
    ) || ["Try your best!"],
    solution:
      bp.editorSettings?.map((es) => es.boilerplateCode).join("\n") || "",
  }));

  const sampleVideoUrl =
    (currentLesson && currentLesson.videoUrl) ||
    currentSection.videoUrl ||
    "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4";

  console.log("Current videoUrl for player:", sampleVideoUrl);

  return (
    <>
      <audio ref={transitionAudioRef} src="https://d279zq803tcyfh.cloudfront.net/transition.mp3" preload="auto" />
      <div className="min-h-screen bg-gradient-to-b from-background via-background/95 to-muted/30">
        {!isFullscreen && (
          <>
            <div className="relative h-40 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-t from-background via-background/70 to-transparent"></div>
              <div className="absolute inset-0 flex items-center">
                <div className="container mx-auto px-4 md:px-6">
                  <div className="max-w-3xl  p-4 mb-2 ">
                    {/* Breadcrumb */}
                    {courseName && moduleNumber && (
                      <div className="text-sm text-gray-400 dark:text-gray-500 flex items-center gap-1">
                        {courseId ? (
                          <Link
                            href={`/course-detail/${courseId}`}
                            className="hover:underline text-primary/95 font-medium"
                          >
                            {courseName}
                          </Link>
                        ) : (
                          <span>{courseName}</span>
                        )}
                        <ChevronRight className="w-3 h-3 mx-1" />
                        <span className="text-xs">Module {moduleNumber}</span>
                        <ChevronRight className="w-3 h-3 mx-1" />
                        <button
                          onClick={() =>
                            router.push(`/module/${modules[0]?.id || ""}`)
                          }
                          className="text-xs font-semibold text-gray-700 dark:text-gray-200 hover:underline hover:text-primary/95 transition-colors"
                        >
                          {title}
                        </button>
                      </div>
                    )}
                    <h1 className="text-xl md:text-2xl font-bold mb-1 text-gray-900 dark:text-white tracking-tight">
                      {title}
                    </h1>
                    <p className="text-gray-500 dark:text-gray-300 text-sm md:text-base mb-3 max-w-2xl">
                      {description}
                    </p>
                    <div className="flex flex-wrap gap-1 mb-1">
                      <Badge
                        variant="secondary"
                        className="bg-gray-100 text-gray-800 border-none dark:bg-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full font-medium text-[10px] flex items-center gap-1"
                      >
                        <Clock className="w-3 h-3 mr-0.5" />
                        {modules.reduce(
                          (total, module) =>
                            total +
                            (module.sections?.reduce(
                              (sectionTotal, section) =>
                                sectionTotal + (section.lessons?.length || 0),
                              0
                            ) || 0),
                          0
                        )}{" "}
                        Lessons
                      </Badge>
                      <Badge
                        variant="secondary"
                        className="bg-gray-100 text-gray-800 border-none dark:bg-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full font-medium text-[10px] flex items-center gap-1"
                      >
                        <Award className="w-3 h-3 mr-0.5" />
                        Certificate Included
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
              {/* Glowing Blurred Gradient Backgrounds */}
              <div className="absolute -top-40 -left-40 w-80 h-80 bg-upskilleo-purple/30 rounded-full blur-3xl z-0"></div>
              <div className="absolute -top-40 -right-20 w-80 h-80 bg-upskilleo-purple/20 rounded-full blur-3xl z-0"></div>
              {/* Animated Sparkle Dots */}
              <div className="absolute top-10 left-24 w-2 h-2 bg-white rounded-full opacity-70 blur-[2px] animate-sparkle1 z-0"></div>
              <div className="absolute top-20 right-32 w-1.5 h-1.5 bg-yellow-300 rounded-full opacity-80 blur-[1.5px] animate-sparkle2 z-0"></div>
              <div className="absolute bottom-8 left-1/3 w-2.5 h-2.5 bg-fuchsia-400 rounded-full opacity-60 blur-[2px] animate-sparkle3 z-0"></div>
              <div className="absolute top-32 right-1/4 w-1.5 h-1.5 bg-cyan-300 rounded-full opacity-70 blur-[1.5px] animate-sparkle4 z-0"></div>
              <div className="absolute bottom-12 right-20 w-2 h-2 bg-emerald-300 rounded-full opacity-60 blur-[2px] animate-sparkle5 z-0"></div>
              <button
                onClick={() => router.back()}
                className="absolute bottom-4 right-4 z-20 group w-14 h-14 rounded-full bg-primary text-white shadow-xl flex items-center justify-center hover:scale-105 hover:bg-primary/90 transition-all duration-200 border-4 border-white/30 backdrop-blur-md"
                title="Browse All Modules in This Course"
                aria-label="Browse All Modules in This Course"
              >
                <List className="w-7 h-7" />
                <span className="absolute right-16 bottom-1/2 translate-y-1/2 opacity-0 group-hover:opacity-100 bg-background text-foreground text-xs rounded px-2 py-1 shadow transition-opacity duration-200 pointer-events-none">
                  Browse All Modules in This Course
                </span>
              </button>
            </div>
          </>
        )}

        <div
          ref={fullscreenRef}
          className={`mt-2 ${
            isFullscreen
              ? "fixed inset-0 z-50 bg-background p-0 m-0 h-full min-h-0 flex flex-col pt-0"
              : "container mx-auto px-4 md:px-6"
          }`}
        >
          <div
            className={`grid ${
              isFullscreen ? "grid-cols-1 h-full min-h-0" : "md:grid-cols-12"
            } gap-2`}
            {...(isFullscreen ? { style: { height: '100%', minHeight: 0 } } : {})}
          >
            {!isFullscreen && (
              <div className="md:col-span-4 lg:col-span-3">
                <div className="bg-card rounded-lg border shadow-sm sticky top-24 transition-all hover:shadow-md bg-gradient-to-br from-[#9b87f5]/5 to-[#7E69AB]/25 shadow-[0_0_12px_0_rgba(156,133,245,0.06)] backdrop-blur-sm">
                  <div className="p-4 border-b">
                    <h3 className="font-semibold flex items-center">
                      <BookOpen className="h-4 w-4 text-primary mr-2" />
                      Module Content
                    </h3>
                  </div>
                  <div className="p-4 max-h-[calc(100vh-220px)] overflow-y-auto upskilleo-scrollbar">
                    <CourseModuleTimeline
                      modules={localModules}
                      currentModuleIndex={currentModuleIndex}
                      currentSectionIndex={currentSectionIndex}
                      currentLessonIndex={currentLessonIndex}
                      onSelectLesson={handleSelectLesson}
                    />
                  </div>
                </div>
              </div>
            )}

            <div
              className={`${
                isFullscreen ? "col-span-full" : "md:col-span-8 lg:col-span-9"
              }`}
            >
              <AnimatePresence mode="wait" initial={false}>
                {showEditor ? (
                  <motion.div
                    key="editor"
                    initial={{
                      opacity: 0,
                      y: 40,
                      filter: "blur(8px)",
                      boxShadow: "0 8px 32px rgba(80,60,180,0.10)"
                    }}
                    animate={{
                      opacity: 1,
                      y: 0,
                      filter: "blur(0px)",
                      boxShadow: "0 4px 24px rgba(80,60,180,0.08)"
                    }}
                    exit={{
                      opacity: 0,
                      y: -40,
                      filter: "blur(8px)",
                      boxShadow: "0 8px 32px rgba(80,60,180,0.10)"
                    }}
                    transition={{
                      duration: 0.55,
                      ease: [0.22, 1, 0.36, 1]
                    }}
                    className={`bg-card rounded-lg border shadow-sm ${isFullscreen ? "max-h-screen flex flex-col h-full min-h-0" : ""}`}
                    style={{ willChange: "opacity, transform, filter, box-shadow" }}
                  >
                    <CourseEditor
                      key={`${currentModuleIndex}-${currentSectionIndex}-${currentLessonIndex}-${
                        currentKeyMoment?.id || "no-breakpoint"
                      }`}
                      code={code}
                      onChange={setCode}
                      language={getCurrentEditorLanguage()}
                      hints={currentKeyMoment?.hints || []}
                      onSkip={handleSkipChallenge}
                      onSubmit={handleSubmitCode}
                      initialHtmlCode={htmlCode}
                      initialCssCode={cssCode}
                      initialJsCode={jsCode}
                      isFullscreen={isFullscreen}
                      onToggleFullscreen={toggleFullscreen}
                      onContinueVideo={handleContinueVideo}
                      onBackToVideo={handleBackToVideo}
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    key="video"
                    initial={{
                      opacity: 0,
                      y: 40,
                      filter: "blur(8px)",
                      boxShadow: "0 8px 32px rgba(80,60,180,0.10)"
                    }}
                    animate={{
                      opacity: 1,
                      y: 0,
                      filter: "blur(0px)",
                      boxShadow: "0 4px 24px rgba(80,60,180,0.08)"
                    }}
                    exit={{
                      opacity: 0,
                      y: -40,
                      filter: "blur(8px)",
                      boxShadow: "0 8px 32px rgba(80,60,180,0.10)"
                    }}
                    transition={{
                      duration: 0.55,
                      ease: [0.22, 1, 0.36, 1]
                    }}
                    className={`bg-card rounded-lg border shadow-sm transition-all hover:shadow-md ${isFullscreen ? "h-full flex flex-col" : ""}`}
                    style={{ willChange: "opacity, transform, filter, box-shadow" }}
                  >
                    {/* Always display the CourseVideoPlayer */}
                    <CourseVideoPlayer
                      ref={videoPlayerRef}
                      key={
                        currentModuleIndex +
                        "-" +
                        currentSectionIndex +
                        "-" +
                        currentLessonIndex
                      }
                      videoUrl={sampleVideoUrl}
                      keyMoments={keyMoments}
                      onKeyMomentEncountered={handleKeyMomentEncountered}
                      onComplete={handleVideoComplete}
                      showVideo={true}
                      isFullscreen={isFullscreen}
                      onToggleFullscreen={toggleFullscreen}
                      resumeVideo={resumeVideo}
                      initialTime={lastPausedTime !== null ? lastPausedTime : (currentLesson?.continueAtDuration || 0)}
                      completedKeyMoments={completedKeyMoments}
                      skippedKeyMoments={skippedKeyMoments}
                      isPlaying={isPlaying}
                      onTogglePlay={handleTogglePlay}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CourseSectionDetail;
