const { connectDB, disconnectDB } = require('../config/database');
const Course = require('../models/Course');
const { courses } = require('../data/courses');

// Function to seed courses
const seedCourses = async () => {
  try {
    console.log('🌱 Starting course seeding...');
    
    // Connect to database
    await connectDB();
    
    // Clear existing courses
    console.log('🗑️ Clearing existing courses...');
    await Course.deleteMany({});
    console.log('✅ Existing courses cleared');
    
    // Insert new courses
    console.log('📝 Inserting courses...');
    const courseData = courses.map(course => ({
      id: course.id,
      slug: course.slug,
      title: course.title,
      description: course.description,
      longDescription: course.longDescription,
      badge: course.badge,
      totalLessons: course.totalLessons,
      level: course.level,
      projects: course.projects,
      image: course.image,
      color: course.color,
      price: course.price,
      instructor: course.instructor,
      rating: course.rating,
      reviews: course.reviews,
      students: course.students,
      lastUpdated: course.lastUpdated,
      features: course.features,
      topics: course.topics,
      resources: course.resources,
      isActive: true
    }));
    
    const insertedCourses = await Course.insertMany(courseData);
    
    console.log(`✅ Successfully seeded ${insertedCourses.length} courses`);
    
    // Display seeded courses
    console.log('\n📊 Seeded Courses:');
    insertedCourses.forEach(course => {
      console.log(`  - ${course.title} (${course.slug}) - $${course.price}`);
    });
    
    console.log('\n🎉 Course seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding courses:', error.message);
    process.exit(1);
  } finally {
    // Disconnect from database
    await disconnectDB();
  }
};

// Function to get course statistics
const getCourseStats = async () => {
  try {
    await connectDB();
    
    const totalCourses = await Course.countDocuments();
    const activeCourses = await Course.countDocuments({ isActive: true });
    const totalStudents = await Course.aggregate([
      { $group: { _id: null, total: { $sum: '$students' } } }
    ]);
    const averageRating = await Course.aggregate([
      { $group: { _id: null, avg: { $avg: '$rating' } } }
    ]);
    
    console.log('\n📈 Course Statistics:');
    console.log(`  Total Courses: ${totalCourses}`);
    console.log(`  Active Courses: ${activeCourses}`);
    console.log(`  Total Students: ${totalStudents[0]?.total || 0}`);
    console.log(`  Average Rating: ${averageRating[0]?.avg?.toFixed(2) || 0}`);
    
  } catch (error) {
    console.error('❌ Error getting course stats:', error.message);
  } finally {
    await disconnectDB();
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'stats') {
    getCourseStats();
  } else {
    seedCourses();
  }
}

module.exports = { seedCourses, getCourseStats }; 