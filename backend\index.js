const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { createToken, authenticateUser } = require('./auth');
const { courses } = require('./data/courses');

// Dynamic user tracking - starts empty and gets populated through API calls
const userTracking = {};

const app = express();
const PORT = 8080;

// CORS middleware to allow cross-origin requests
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'], // Allow frontend origins
  credentials: true, // Allow credentials (cookies, authorization headers)
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // Allowed methods
  allowedHeaders: ['Content-Type', 'Authorization', 'x-client-time'], // Allowed headers
}));

// Middleware to parse JSON bodies
app.use(bodyParser.json());

//crea

// Dummy login route
app.post('/login', (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({ message: 'Email and password are required' });
  }

  const DUMMY_EMAIL = '<EMAIL>';
  const DUMMY_PASSWORD = '123456';

  if (email !== DUMMY_EMAIL || password !== DUMMY_PASSWORD) {
    return res.status(401).json({ message: 'Invalid credentials' });
  }

  const user = { id: '123', email };
  const accessToken = createToken(user);

  // Get user enrollment data
  const userData = userTracking[email] || { enrolledCourses: {} };
  const enrolledCoursesCount = Object.keys(userData.enrolledCourses).length;
  const enrolledCourseIds = Object.keys(userData.enrolledCourses);

  return res.status(200).json({
    message: 'Login successful',
    accessToken,
    user: {
      id: user.id,
      email: user.email,
      name: 'Test User',
      enrolledCoursesCount,
      enrolledCourseIds
    }
  });
});

// New endpoint to get user enrollment information
app.get('/user/enrollments', authenticateUser, (req, res) => {
  const userEmail = req.user.email;

  // Get user enrollment data
  const userData = userTracking[userEmail] || { enrolledCourses: {} };
  const enrolledCourses = Object.keys(userData.enrolledCourses);

  // Get course details for enrolled courses
  const enrolledCourseDetails = enrolledCourses.map(courseId => {
    const course = courses.find(c => c.id == courseId);
    if (course) {
      return {
        id: course.id,
        slug: course.slug,
        title: course.title,
        description: course.description,
        image: course.image,
        progress: userData.enrolledCourses[courseId].progress || 0,
        completedLessons: userData.enrolledCourses[courseId].completedLessons || 0,
        enrolledAt: userData.enrolledCourses[courseId].enrolledAt || new Date().toISOString()
      };
    }
    return null;
  }).filter(course => course !== null);

  res.status(200).json({
    enrolledCoursesCount: enrolledCourses.length,
    enrolledCourses: enrolledCourseDetails
  });
});

// New endpoint to get user details and enrollment status for a specific course
app.get('/user/details/:courseId', authenticateUser, (req, res) => {
  const userEmail = req.user.email;
  const courseId = req.params.courseId;
  const userData = userTracking[userEmail] || { enrolledCourses: {} };
  const enrolledCourses = Object.keys(userData.enrolledCourses);
  const isEnrolledInCourse = enrolledCourses.includes(courseId);
  let courseDetails = null;
  if (isEnrolledInCourse) {
    const course = courses.find(c => c.id == courseId);
    if (course) {
      const enrollment = userData.enrolledCourses[courseId];
      // Merge static course data with user progress
      //this is for making firsy modeul in progress when user is freshly enrolled
      let flag = false;
      const mergedModules = course.modules.map((module) => {
        const userModule = (enrollment.modules || []).find(m => m.moduleId === module.id) || {};
        // Merge sections
        const mergedSections = (module.sections || []).map(section => {
          const userSection = (userModule.sections || []).find(s => s.sectionId === section.id) || {};
          return {
            ...section,
            completed: userSection.completed ?? section.completed ?? false,
            locked: userSection.locked ?? section.locked ?? false,
            status: userSection.status ?? section.status ?? 'not_started',
            completedAt: userSection.completedAt || null
          };
        });
        // Calculate module progress
        const totalSections = mergedSections.length;
        const completedSections = mergedSections.filter(s => s.completed).length;
        let moduleProgress = totalSections > 0 ? Math.round((completedSections / totalSections) * 100) : 0;
        if (!flag && moduleProgress === 0) {
          moduleProgress = 0.1;
          flag = true;
        }

        // Merge lessons
        const mergedLessons = [].concat(...(mergedSections).map(s => s.lessons || []));
        return {
          ...module,
          progress: moduleProgress,
          status: userModule.status ?? (moduleProgress === 100 ? 'completed' : (moduleProgress > 0 ? 'in_progress' : 'not_started')),
          completed: moduleProgress === 100,
          lastWatchedLessonId: userModule.lastWatchedLessonId || null,
          completedLessons: completedSections,
          totalLessons: totalSections,
          sections: mergedSections,
          lessons: mergedLessons
        };
      });
      // Calculate overall course progress
      const totalSections = mergedModules.reduce((sum, m) => sum + (m.sections?.length || 0), 0);
      const completedSections = mergedModules.reduce((sum, m) => sum + (m.sections?.filter(s => s.completed).length || 0), 0);
      const courseProgress = totalSections > 0 ? Math.round((completedSections / totalSections) * 100) : 0;
      courseDetails = {
        id: course.id,
        slug: course.slug,
        title: course.title,
        description: course.description,
        longDescription: course.longDescription,
        image: course.image,
        price: course.price,
        instructor: course.instructor,
        rating: course.rating,
        reviews: course.reviews,
        students: course.students,
        level: course.level,
        lastUpdated: course.lastUpdated,
        features: course.features,
        topics: course.topics,
        progress: courseProgress,
        completedLessons: completedSections,
        enrolledAt: enrollment.enrolledAt || new Date().toISOString(),
        lastAccessed: enrollment.lastAccessed || new Date().toISOString(),
        modules: mergedModules
      };
    }
  }

  res.status(200).json({
    user: {
      id: req.user.id || '123',
      email: userEmail,
      name: 'Test User',
      enrolledCoursesCount: enrolledCourses.length,
      enrolledCourseIds: enrolledCourses
    },
    courseEnrollment: {
      isEnrolled: isEnrolledInCourse,
      courseDetails
    }
  });
});

// New endpoint to get general user details (without courseId)
app.get('/user/details', authenticateUser, (req, res) => {
  const userEmail = req.user.email;

  // Get user enrollment data
  const userData = userTracking[userEmail] || { enrolledCourses: {} };
  const enrolledCourses = Object.keys(userData.enrolledCourses);

  res.status(200).json({
    user: {
      id: req.user.id || '123',
      email: userEmail,
      name: 'Test User',
      enrolledCoursesCount: enrolledCourses.length,
      enrolledCourseIds: enrolledCourses
    }
  });
});

app.get('/courses', (req, res) => {
  res.status(200).json(courses);
});

app.post('/enroll/:courseId', authenticateUser, (req, res) => {
  const userEmail = req.user.email;
  const courseId = req.params.courseId;
  const course = courses.find(c => c.id == courseId);
  if (!course) {
    return res.status(404).json({ message: 'Course not found' });
  }
  if (!userTracking[userEmail]) {
    userTracking[userEmail] = { enrolledCourses: {} };
  }
  if (userTracking[userEmail].enrolledCourses[courseId]) {
    return res.status(400).json({ message: 'User already enrolled in this course' });
  }
  // Build initial progress structure for all modules/sections/lessons
  const modulesProgress = course.modules.map(module => ({
    moduleId: module.id,
    progress: 0,
    status: 'not_started',
    lastWatchedLessonId: null,
    completedLessons: 0,
    totalLessons: (module.sections || []).length,
    sections: (module.sections || []).map(section => ({
      sectionId: section.id,
      status: 'not_started',
      completed: false,
      locked: !!section.locked,
      completedAt: null
    })),
    lessons: [].concat(...(module.sections || []).map(s => s.lessons || []))
  }));
  userTracking[userEmail].enrolledCourses[courseId] = {
    progress: 0,
    completedLessons: 0,
    enrolledAt: new Date().toISOString(),
    lastAccessed: new Date().toISOString(),
    modules: modulesProgress
  };
  const enrolledCourseIds = Object.keys(userTracking[userEmail].enrolledCourses);
  res.status(200).json({
    message: `User ${userEmail} enrolled successfully in course ${courseId}`,
    enrolledCoursesCount: enrolledCourseIds.length,
    enrolledCourseIds
  });
});


app.get('/course-details/:courseId', authenticateUser, (req, res) => {
  const userEmail = req.user.email;
  const courseId = req.params.courseId;

  // Find the course
  const course = courses.find(c => c.id == courseId);
  if (!course) {
    return res.status(404).json({ message: 'Course not found' });
  }

  // Get user's enrollment data
  const userData = userTracking[userEmail] || { enrolledCourses: {} };
  const enrollment = userData.enrolledCourses[courseId];

  if (!enrollment) {
    // User not enrolled
    return res.status(200).json({ enrolled: false });
  }

  // Build detailed response with progress info
  const detailedCourse = {
    enrolled: true,
    title: course.title,
    description: course.description,
    progress: enrollment.progress,
    completedLessons: enrollment.completedLessons,
    totalLessons: course.totalLessons,
    resources: course.resources || [],

    modules: course.modules.map(module => ({
      title: module.title,
      description: module.description,
      progress: enrollment.progress, // you can customize per module if desired
      totalHours: module.totalHours,
      totalLessons: module.totalLessons,
      labels: module.labels || [],
      lessons: [].concat(...(module.sections || []).map(s => s.lessons || []))
    })),
  };

  res.status(200).json(detailedCourse);
});

// New API endpoint for course enrollment data (Your Learning Roadmap section)
app.get('/enroll/course/:courseId', (req, res) => {
  const courseId = req.params.courseId;

  // Find the course by slug
  const course = courses.find(c => c.slug === courseId || c.id === courseId);
  if (!course) {
    return res.status(404).json({
      success: false,
      message: 'Course not found'
    });
  }

  // Return the course data needed for the Learning Roadmap section
  const roadmapData = {
    success: true,
    course: {
      id: course.id,
      slug: course.slug,
      title: course.title,
      description: course.description,
      longDescription: course.longDescription,
      image: course.image,
      price: course.price,
      instructor: course.instructor,
      rating: course.rating,
      reviews: course.reviews,
      students: course.students,
      level: course.level,
      lastUpdated: course.lastUpdated,
      features: course.features,
      topics: course.topics,
      modules: course.modules.map(module => ({
        id: module.id,
        title: module.title,
        description: module.description,
        duration: module.duration,
        lessons: [].concat(...(module.sections || []).map(s => s.lessons || [])),
        completed: module.completed,
        locked: module.locked,
        progress: module.progress,
        badge: module.badge,
        track: module.track,
        topics: module.topics,
        sections: module.sections,
        totalHours: module.totalHours,
        totalLessons: module.totalLessons,
        labels: module.labels
      }))
    }
  };

  res.status(200).json(roadmapData);
});

app.get('/module-details/:moduleId', authenticateUser, (req, res) => {
  const userEmail = req.user.email;
  const moduleId = req.params.moduleId;

  // Find the module in any course
  let foundModule = null;
  let courseId = null;

  for (const course of courses) {
    const module = course.modules.find(m => m.id === moduleId);
    if (module) {
      foundModule = module;
      courseId = course.id;
      break;
    }
  }

  if (!foundModule) {
    return res.status(404).json({ message: 'Module not found' });
  }

  // Get user's enrollment data
  const userData = userTracking[userEmail] || { enrolledCourses: {} };
  const enrollment = userData.enrolledCourses[courseId];

  if (!enrollment) {
    return res.status(403).json({ message: 'User not enrolled in this course' });
  }

  // Get module progress from user tracking
  const moduleProgress = enrollment.modules?.[moduleId] || {
    status: 'not_started',
    lastWatchedLessonId: null,
    lessons: {}
  };

  // Build the response according to the contract
  const moduleDetails = {
    id: foundModule.id,
    title: foundModule.title,
    description: foundModule.description,
    labels: foundModule.labels || [],
    lastWatchedLessonId: moduleProgress.lastWatchedLessonId,
    courseName: courseId ? (courses.find(c => c.id === courseId)?.title || "") : "",
    moduleNumber: courseId ? (courses.find(c => c.id === courseId)?.modules.findIndex(m => m.id === foundModule.id) + 1) : null,
    courseId: courseId || null,
    sections: foundModule.sections.map((section, idx) => ({
      id: section.id,
      title: section.title,
      description: section.description || '',
      duration: section.duration,
      completed: section.completed,
      locked: section.locked,
      status: moduleProgress.status || 'not_started',
      lessons: [].concat(...(section.lessons || []).map(lesson => {
        // If you have lesson progress tracking, you can add it here
        // Get lesson status from userTracking if available
        let lessonStatus = 'not_started';
        if (moduleProgress.lessons && moduleProgress.lessons[lesson.id] && moduleProgress.lessons[lesson.id].status) {
          lessonStatus = moduleProgress.lessons[lesson.id].status;
        }
        return {
          id: lesson.id,
          title: lesson.title,
          description: lesson.description,
          status: idx < 2 ? 'completed' : lessonStatus, // use tracked status if available

          totalDuration: lesson.duration,
          videoUrl: lesson.videoUrl,
          continueAtDuration: 0, // or use lesson.continueAtDuration if available
          breakpoints: lesson.breakpoints || [],
          locked: lesson.locked // If you have locked on lessons, include it here
        };
      }))
    }))
  };

  res.status(200).json(moduleDetails);
});

app.post('/lesson/progress/:moduleId/:lessonId', authenticateUser, (req, res) => {
  const userEmail = req.user.email;
  const { moduleId, lessonId } = req.params;
  const { duration } = req.body;

  if (!duration || typeof duration !== 'number') {
    return res.status(400).json({ message: 'Duration is required and must be a number' });
  }

  // Find the module in any course
  let foundModule = null;
  let courseId = null;

  for (const course of courses) {
    const module = course.modules.find(m => m.id === moduleId);
    if (module) {
      foundModule = module;
      courseId = course.id;
      break;
    }
  }

  if (!foundModule) {
    return res.status(404).json({ message: 'Module not found' });
  }

  // Verify lesson exists in module
  const lesson = foundModule.lessons.find(l => l.id === lessonId);
  if (!lesson) {
    return res.status(404).json({ message: 'Lesson not found in module' });
  }

  // Get user's enrollment data
  const userData = userTracking[userEmail] || { enrolledCourses: {} };
  const enrollment = userData.enrolledCourses[courseId];

  if (!enrollment) {
    return res.status(403).json({ message: 'User not enrolled in this course' });
  }

  // Initialize module tracking if not exists
  if (!enrollment.modules) {
    enrollment.modules = {};
  }
  if (!enrollment.modules[moduleId]) {
    enrollment.modules[moduleId] = {
      status: 'not_started',
      lastWatchedLessonId: null,
      lessons: {}
    };
  }

  // Initialize lesson tracking if not exists
  if (!enrollment.modules[moduleId].lessons) {
    enrollment.modules[moduleId].lessons = {};
  }
  if (!enrollment.modules[moduleId].lessons[lessonId]) {
    enrollment.modules[moduleId].lessons[lessonId] = {
      status: 'not_started',
      continueAtDuration: 0
    };
  }

  // Update lesson progress
  enrollment.modules[moduleId].lessons[lessonId].continueAtDuration = duration;
  enrollment.modules[moduleId].lastWatchedLessonId = lessonId;

  // If lesson is not started, mark it as in_progress
  if (enrollment.modules[moduleId].lessons[lessonId].status === 'not_started') {
    enrollment.modules[moduleId].lessons[lessonId].status = 'in_progress';
  }

  // If module is not started, mark it as in_progress
  if (enrollment.modules[moduleId].status === 'not_started') {
    enrollment.modules[moduleId].status = 'in_progress';
  }

  res.status(200).json({
    marked: true
  });
});

app.post('/lesson/breakpoint/:moduleId/:lessonId/:breakpointId', authenticateUser, (req, res) => {
  const userEmail = req.user.email;
  const { moduleId, lessonId, breakpointId } = req.params;
  const { completed, editorSettings } = req.body;

  if (typeof completed !== 'boolean') {
    return res.status(400).json({ message: 'Completed status is required and must be a boolean' });
  }

  if (!editorSettings || !Array.isArray(editorSettings)) {
    return res.status(400).json({ message: 'Editor settings are required and must be an array' });
  }

  // Find the module in any course
  let foundModule = null;
  let courseId = null;

  for (const course of courses) {
    const module = course.modules.find(m => m.id === moduleId);
    if (module) {
      foundModule = module;
      courseId = course.id;
      break;
    }
  }

  if (!foundModule) {
    return res.status(404).json({ message: 'Module not found' });
  }

  // Verify lesson exists in module
  const lesson = foundModule.lessons.find(l => l.id === lessonId);
  if (!lesson) {
    return res.status(404).json({ message: 'Lesson not found in module' });
  }

  // Verify breakpoint exists with the specified ID
  const breakpoint = lesson.breakpoints.find(b => b.id === breakpointId);
  if (!breakpoint) {
    return res.status(404).json({ message: 'Breakpoint not found with specified ID' });
  }

  // Get user's enrollment data
  const userData = userTracking[userEmail] || { enrolledCourses: {} };
  const enrollment = userData.enrolledCourses[courseId];

  if (!enrollment) {
    return res.status(403).json({ message: 'User not enrolled in this course' });
  }

  // Initialize module tracking if not exists
  if (!enrollment.modules) {
    enrollment.modules = {};
  }
  if (!enrollment.modules[moduleId]) {
    enrollment.modules[moduleId] = {
      status: 'not_started',
      lastWatchedLessonId: null,
      lessons: {}
    };
  }

  // Initialize lesson tracking if not exists
  if (!enrollment.modules[moduleId].lessons) {
    enrollment.modules[moduleId].lessons = {};
  }
  if (!enrollment.modules[moduleId].lessons[lessonId]) {
    enrollment.modules[moduleId].lessons[lessonId] = {
      status: 'not_started',
      continueAtDuration: 0,
      breakpoints: {}
    };
  }

  // Initialize breakpoint tracking if not exists
  if (!enrollment.modules[moduleId].lessons[lessonId].breakpoints) {
    enrollment.modules[moduleId].lessons[lessonId].breakpoints = {};
  }
  if (!enrollment.modules[moduleId].lessons[lessonId].breakpoints[breakpointId]) {
    enrollment.modules[moduleId].lessons[lessonId].breakpoints[breakpointId] = {
      completed: false,
      editorSettings: {}
    };
  }

  // Update breakpoint data
  enrollment.modules[moduleId].lessons[lessonId].breakpoints[breakpointId].completed = completed;

  // Update editor settings
  editorSettings.forEach(setting => {
    if (!enrollment.modules[moduleId].lessons[lessonId].breakpoints[breakpointId].editorSettings[setting.type]) {
      enrollment.modules[moduleId].lessons[lessonId].breakpoints[breakpointId].editorSettings[setting.type] = {
        userCode: ""
      };
    }
    enrollment.modules[moduleId].lessons[lessonId].breakpoints[breakpointId].editorSettings[setting.type].userCode = setting.userCode;
  });

  // Update lesson status if all breakpoints are completed
  const allBreakpointsCompleted = lesson.breakpoints.every(bp =>
    enrollment.modules[moduleId].lessons[lessonId].breakpoints[bp.id]?.completed
  );

  if (allBreakpointsCompleted) {
    enrollment.modules[moduleId].lessons[lessonId].status = 'completed';
  }

  // Update module status if all lessons are completed
  const allLessonsCompleted = foundModule.lessons.every(l =>
    enrollment.modules[moduleId].lessons[l.id]?.status === 'completed'
  );

  if (allLessonsCompleted) {
    enrollment.modules[moduleId].status = 'completed';
  }

  res.status(200).json({
    marked: true
  });
});

// New comprehensive endpoint to get enrolled courses with full details for learning dashboard
app.get('/user/enrolled-courses', authenticateUser, (req, res) => {
  const userEmail = req.user.email;

  // Get user enrollment data
  const userData = userTracking[userEmail] || { enrolledCourses: {} };
  const enrolledCourseIds = Object.keys(userData.enrolledCourses);

  // Get comprehensive course details for enrolled courses
  const enrolledCourses = enrolledCourseIds.map(courseId => {
    const course = courses.find(c => c.id === courseId);
    if (!course) return null;

    const enrollmentData = userData.enrolledCourses[courseId];
    const progress = enrollmentData?.progress || 0;
    const completedLessons = enrollmentData?.completedLessons || 0;
    const enrolledAt = enrollmentData?.enrolledAt || new Date().toISOString();

    // Calculate last accessed date
    const lastAccessed = enrollmentData?.lastAccessed || enrolledAt;

    // Determine next lesson based on progress
    let nextLesson = "Course not started";
    if (progress > 0 && progress < 100) {
      // Find the next incomplete module
      const nextModule = course.modules?.find(module => !module.completed);
      if (nextModule) {
        nextLesson = nextModule.title;
      }
    } else if (progress === 100) {
      nextLesson = "Course Completed";
    }

    // Calculate total lessons and completed lessons
    const totalLessons = course.modules?.reduce((total, module) => total + (module.lessons?.length || 0), 0) || 0;

    // Calculate total duration
    const totalDuration = course.modules?.reduce((total, module) => {
      const moduleHours = parseInt(module.duration?.match(/(\d+)/)?.[1] || '0');
      return total + moduleHours;
    }, 0) || 0;

    return {
      id: course.id,
      slug: course.slug,
      title: course.title,
      description: course.description,
      longDescription: course.longDescription,
      image: course.image,
      level: course.level,
      instructor: course.instructor,
      rating: course.rating,
      reviews: course.reviews,
      students: course.students,
      lastUpdated: course.lastUpdated,
      price: course.price,

      // Enrollment specific data
      progress: progress,
      completedLessons: completedLessons,
      totalLessons: totalLessons,
      enrolledAt: enrolledAt,
      lastAccessed: lastAccessed,
      nextLesson: nextLesson,

      // Course structure
      duration: `${totalDuration} weeks`,
      lessons: totalLessons,
      modules: course.modules?.length || 0,

      // Status
      isCompleted: progress === 100,
      isInProgress: progress > 0 && progress < 100,
      isNotStarted: progress === 0,

      // Features and topics
      features: course.features || [],
      topics: course.topics || [],

      // Badge and color
      badge: course.badge,
      color: course.color
    };
  }).filter(course => course !== null);

  // Calculate learning statistics
  const totalCourses = enrolledCourses.length;
  const completedCourses = enrolledCourses.filter(course => course.isCompleted).length;
  const inProgressCourses = enrolledCourses.filter(course => course.isInProgress).length;
  const averageProgress = totalCourses > 0
    ? Math.round(enrolledCourses.reduce((sum, course) => sum + course.progress, 0) / totalCourses)
    : 0;

  // Calculate total minutes learned (estimated)
  const totalMinutesLearned = enrolledCourses.reduce((total, course) => {
    const courseHours = parseInt(course.duration.match(/(\d+)/)?.[1] || '0');
    const progressMultiplier = course.progress / 100;
    return total + (courseHours * 60 * progressMultiplier);
  }, 0);

  res.status(200).json({
    success: true,
    data: {
      user: {
        id: req.user.id || '123',
        email: userEmail,
        name: 'Test User',
        enrolledCoursesCount: totalCourses
      },
      enrolledCourses: enrolledCourses,
      statistics: {
        totalCourses,
        completedCourses,
        inProgressCourses,
        averageProgress,
        totalMinutesLearned: Math.round(totalMinutesLearned)
      }
    }
  });
});

// Dynamic enrollment management endpoints

// Add sample enrollments for testing (optional endpoint)
app.post('/admin/add-sample-enrollments', authenticateUser, (req, res) => {
  const userEmail = req.user.email;

  // Initialize user tracking if missing
  if (!userTracking[userEmail]) {
    userTracking[userEmail] = { enrolledCourses: {} };
  }

  // Add sample enrollments
  userTracking[userEmail].enrolledCourses['frontend'] = {
    progress: 65,
    completedLessons: 8,
    lessons: {},
    enrolledAt: '2024-01-15T10:00:00.000Z',
    lastAccessed: '2024-01-20T14:30:00.000Z'
  };

  userTracking[userEmail].enrolledCourses['javascript'] = {
    progress: 100,
    completedLessons: 12,
    lessons: {},
    enrolledAt: '2024-01-10T09:00:00.000Z',
    lastAccessed: '2024-01-18T16:45:00.000Z'
  };

  res.status(200).json({
    message: 'Sample enrollments added successfully',
    enrolledCourses: Object.keys(userTracking[userEmail].enrolledCourses)
  });
});

// Update course progress
app.put('/enrollment/:courseId/progress', authenticateUser, (req, res) => {
  const userEmail = req.user.email;
  const courseId = req.params.courseId;
  const { progress, completedLessons } = req.body;

  // Validate input
  if (typeof progress !== 'number' || progress < 0 || progress > 100) {
    return res.status(400).json({ message: 'Progress must be a number between 0 and 100' });
  }

  if (typeof completedLessons !== 'number' || completedLessons < 0) {
    return res.status(400).json({ message: 'Completed lessons must be a non-negative number' });
  }

  // Check if user is enrolled
  const userData = userTracking[userEmail];
  if (!userData || !userData.enrolledCourses[courseId]) {
    return res.status(404).json({ message: 'User not enrolled in this course' });
  }

  // Update progress
  userData.enrolledCourses[courseId].progress = progress;
  userData.enrolledCourses[courseId].completedLessons = completedLessons;
  userData.enrolledCourses[courseId].lastAccessed = new Date().toISOString();

  res.status(200).json({
    message: 'Progress updated successfully',
    courseId,
    progress,
    completedLessons
  });
});

// Remove enrollment
app.delete('/enrollment/:courseId', authenticateUser, (req, res) => {
  const userEmail = req.user.email;
  const courseId = req.params.courseId;

  // Check if user is enrolled
  const userData = userTracking[userEmail];
  if (!userData || !userData.enrolledCourses[courseId]) {
    return res.status(404).json({ message: 'User not enrolled in this course' });
  }

  // Remove enrollment
  delete userData.enrolledCourses[courseId];

  res.status(200).json({
    message: 'Enrollment removed successfully',
    courseId
  });
});

// Get all enrollments for admin purposes
app.get('/admin/enrollments', authenticateUser, (req, res) => {
  res.status(200).json({
    userTracking,
    totalUsers: Object.keys(userTracking).length
  });
});

// Clear all enrollments for a user
app.delete('/admin/clear-enrollments/:email', authenticateUser, (req, res) => {
  const userEmail = req.params.email;

  if (userTracking[userEmail]) {
    delete userTracking[userEmail];
    res.status(200).json({
      message: 'All enrollments cleared for user',
      userEmail
    });
  } else {
    res.status(404).json({
      message: 'User not found',
      userEmail
    });
  }
});

// Public course details endpoint (no authentication required)
app.get('/public/course-details/:courseId', (req, res) => {
  const courseId = req.params.courseId;
  // Find the course by slug or id
  const course = courses.find(c => c.slug === courseId || c.id === courseId);
  if (!course) {
    return res.status(404).json({
      success: false,
      message: 'Course not found'
    });
  }
  // Return public course data (no user/enrollment info)
  const publicData = {
    id: course.id,
    slug: course.slug,
    title: course.title,
    description: course.description,
    longDescription: course.longDescription,
    image: course.image,
    price: course.price,
    instructor: course.instructor,
    rating: course.rating,
    reviews: course.reviews,
    students: course.students,
    level: course.level,
    lastUpdated: course.lastUpdated,
    features: course.features,
    topics: course.topics,
    modules: course.modules.map(module => ({
      id: module.id,
      title: module.title,
      description: module.description,
      duration: module.duration,
      lessons: [].concat(...(module.sections || []).map(s => s.lessons || [])),
      completed: module.completed,
      locked: module.locked,
      progress: module.progress,
      badge: module.badge,
      track: module.track,
      topics: module.topics,
      sections: module.sections,
      totalHours: module.totalHours,
      totalLessons: module.totalLessons,
      labels: module.labels
    }))
  };
  res.status(200).json({ success: true, course: publicData });
});

// Lightweight endpoint to check if user is enrolled in a course
app.get('/enrollment-status/:courseId', authenticateUser, (req, res) => {
  const userEmail = req.user.email;
  const courseId = req.params.courseId;
  const userData = userTracking[userEmail] || { enrolledCourses: {} };
  const isEnrolled = Object.keys(userData.enrolledCourses).includes(courseId);
  res.status(200).json({ isEnrolled });
});

app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
