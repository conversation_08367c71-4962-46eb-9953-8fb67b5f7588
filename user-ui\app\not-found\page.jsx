
import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from 'lucide-react';

const NotFound = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center p-6 max-w-md">
        <div className="text-6xl font-bold mb-6">
          <span className="text-gradient">404</span>
        </div>
        <h1 className="text-3xl font-bold mb-4">Page Not Found</h1>
        <p className="text-muted-foreground mb-8">
          Sorry, the page you are looking for doesn't exist or has been moved.
        </p>
        <Button asChild>
          <a href="/">
            <ArrowLeft size={16} className="mr-2" />
            Back to Homepage
          </a>
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
