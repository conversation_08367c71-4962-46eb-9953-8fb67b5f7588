API Documentation for Learning Platform
=====================================

Base URL: http://localhost:8080

1. Login
--------
POST /login
Headers:
  Content-Type: application/json
Body:
{
    "email": "<EMAIL>",
    "password": "123456"
}
Response:
{
    "message": "Login successful",
    "accessToken": "eyJhbGciOiJIUzI1NiIs..."
}

2. Get All Courses
-----------------
GET /courses
Headers:
  Authorization: Bearer <access_token>
Response: Array of all courses

3. Enroll in Course
------------------
POST /enroll/:courseId
Headers:
  Authorization: Bearer <access_token>
Example: POST /enroll/1
Response:
{
    "message": "User <EMAIL> enrolled successfully in course 1"
}

4. Get Course Details
--------------------
GET /course-details/:courseId
Headers:
  Authorization: Bearer <access_token>
Example: GET /course-details/1
Response:
{
    "enrolled": true,
    "title": "Frontend Development",
    "description": "Master HTML, CSS, JavaScript, and modern frontend frameworks.",
    "progress": 0,
    "completedLessons": 0,
    "totalLessons": 15,
    "resources": [...],
    "modules": [...]
}

5. Get Module Details
--------------------
GET /module-details/:moduleId
Headers:
  Authorization: Bearer <access_token>
Example: GET /module-details/html-css-basics
Response:
{
    "id": "html-css-basics",
    "title": "HTML & CSS Basics",
    "description": "Learn the building blocks of the web.",
    "labels": [...],
    "lastWatchedLessonId": null,
    "sections": [{
        "id": "html-css-basics",
        "title": "HTML & CSS Basics",
        "description": "Learn the building blocks of the web.",
        "status": "not_started",
        "lessons": [...]
    }]
}

6. Update Lesson Progress
------------------------
POST /lesson/progress/:moduleId/:lessonId
Headers:
  Authorization: Bearer <access_token>
  Content-Type: application/json
Example: POST /lesson/progress/html-css-basics/html1
Body:
{
    "duration": 150
}
Response:
{
    "marked": true
}

7. Submit Breakpoint Task
------------------------
POST /lesson/breakpoint/:moduleId/:lessonId/:breakpointId
Headers:
  Authorization: Bearer <access_token>
  Content-Type: application/json
Example: POST /lesson/breakpoint/html-css-basics/html1/html1-bp1
Body:
{
    "completed": true,
    "editorSettings": [
        {
            "type": "html",
            "userCode": "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n<h1>Hello World</h1>\n</body>\n</html>"
        },
        {
            "type": "css",
            "userCode": "h1 { color: blue; }"
        },
        {
            "type": "js",
            "userCode": "console.log('Hello');"
        }
    ]
}
Response:
{
    "marked": true
}

Available IDs
============

Module IDs:
- html-css-basics
- js-fundamentals
- react-essentials
- node-fundamentals
- express-framework
- databases-deployment
- frontend-recap
- backend-recap
- project-based-learning

Lesson IDs (for html-css-basics module):
- html1
- css1
- css2

Breakpoint IDs (for html1 lesson):
- html1-bp1

Testing Flow
===========
1. Login to get access token
2. Use token in Authorization header for all other requests
3. Enroll in a course
4. Get module details
5. Update lesson progress
6. Submit breakpoint tasks 