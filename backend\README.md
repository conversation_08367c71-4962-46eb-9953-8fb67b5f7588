# User UI Backend

Backend API for the User UI learning platform with MongoDB database integration.

## 🚀 Quick Start

### Prerequisites
- Node.js (>= 16.0.0)
- MongoDB (local or cloud instance)
- npm or yarn

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   Create a `.env` file in the backend directory:
   ```env
   # Database Configuration
   MONGODB_URI=mongodb://localhost:27017/user-ui-dev
   MONGODB_TEST_URI=mongodb://localhost:27017/user-ui-test
   
   # Environment
   NODE_ENV=development
   
   # Server Configuration
   PORT=5000
   ```

3. **Start MongoDB:**
   Make sure MongoDB is running on your system or use a cloud instance.

### Database Setup

1. **Test database connection:**
   ```bash
   npm run db:test
   ```

2. **Seed the database with course data:**
   ```bash
   npm run seed
   ```

3. **View course statistics:**
   ```bash
   npm run seed:stats
   ```

## 📁 Project Structure

```
backend/
├── config/
│   └── database.js          # Database connection configuration
├── models/
│   └── Course.js            # Course schema and model
├── scripts/
│   └── seedCourses.js       # Database seeding script
├── test/
│   └── testConnection.js    # Database connection tests
├── data/
│   └── courses.js           # Course data
└── package.json
```

## 🗄️ Database Schema

### Courses Collection
The main course information including:
- Basic metadata (id, slug, title, description)
- Pricing and instructor information
- Course statistics (rating, reviews, students)
- Features, topics, and resources
- Timestamps and active status

### Schema Features
- **Validation**: Required fields, data types, and constraints
- **Indexing**: Optimized queries for slug, id, instructor, level
- **Virtual Fields**: Formatted price display
- **Instance Methods**: Course statistics calculation
- **Static Methods**: Find courses by level or instructor
- **Middleware**: Automatic timestamp updates

## 🛠️ Available Scripts

- `npm start` - Start the production server
- `npm run dev` - Start development server with nodemon
- `npm run test` - Run database connection tests
- `npm run seed` - Seed database with course data
- `npm run seed:stats` - View course statistics
- `npm run db:connect` - Test database connection only
- `npm run db:test` - Run comprehensive database tests

## 🔧 Database Operations

### Creating a Course
```javascript
const course = new Course({
  id: 'course-id',
  slug: 'course-slug',
  title: 'Course Title',
  // ... other fields
});
await course.save();
```

### Finding Courses
```javascript
// Find by slug
const course = await Course.findOne({ slug: 'frontend' });

// Find by level
const beginnerCourses = await Course.findByLevel('Beginner');

// Find by instructor
const instructorCourses = await Course.findByInstructor('Sarah Johnson');
```

### Getting Course Statistics
```javascript
const stats = course.getStats();
console.log(stats); // { totalStudents, averageRating, totalReviews, completionRate }
```

## 🌍 Environment Configuration

The application supports different environments:

- **Development**: `mongodb://localhost:27017/user-ui-dev`
- **Production**: Use your production MongoDB URI
- **Test**: `mongodb://localhost:27017/user-ui-test`

## 📊 Database Indexes

The Course schema includes indexes for optimal performance:
- `slug` - For course lookup by URL slug
- `id` - For course lookup by ID
- `instructor` - For filtering by instructor
- `level` - For filtering by difficulty level
- `isActive` - For active course filtering

## 🔒 Security Features

- Input validation and sanitization
- Rate limiting (when Express is configured)
- Helmet.js for security headers
- Environment-based configuration

## 🧪 Testing

Run the database tests to verify everything is working:
```bash
npm run test
```

This will test:
- Database connection
- Course model creation
- Virtual fields and methods
- Static methods
- Basic CRUD operations

## 📝 Next Steps

1. Create additional schemas for:
   - Modules
   - Sections
   - Lessons
   - Breakpoints
   - Users
   - UserProgress
   - Enrollments

2. Set up Express.js server with API routes
3. Implement authentication and authorization
4. Add API endpoints for course operations
5. Set up proper error handling and logging

## 🤝 Contributing

1. Create a feature branch
2. Make your changes
3. Test the database operations
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details 