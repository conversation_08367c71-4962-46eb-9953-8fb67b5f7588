
import React from 'react';
import { Info, <PERSON>R<PERSON>, Code2, <PERSON>, Play } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";

interface EditorHeaderProps {
  language: string;
  hints: string[];
  showHints: boolean;
  showPreview: boolean;
  onToggleHints: () => void;
  onTogglePreview: () => void;
  onRefreshPreview?: () => void;
  onChangeMode?: (mode: 'web') => void;
  editorMode?: 'web';
}

const EditorHeader: React.FC<EditorHeaderProps> = ({
  language,
  hints,
  showHints,
  showPreview,
  onToggleHints,
  onTogglePreview,
  onRefreshPreview,
  onChangeMode,
  editorMode = 'web'
}) => {
  // Get the proper VS Code icon for the language
  const getLanguageLabel = (lang: string) => {
    switch (lang.toLowerCase()) {
      case 'html': return 'HTML';
      case 'css': return 'CSS';
      case 'js': case 'javascript': return 'JavaScript';
      default: return lang.charAt(0).toUpperCase() + lang.slice(1);
    }
  };

  return (
    <div className="bg-[#1E1E1E] px-3 py-2 border-b border-[#3E3E3E] flex items-center justify-between">
      <div className="flex items-center gap-2">
        <div className="bg-[#2D2D2D] text-xs px-2 py-1 rounded text-gray-300 border border-[#3E3E3E]">
          {getLanguageLabel(language)}
        </div>
      </div>
      <div className="flex items-center gap-2">
        {hints.length > 0 && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-7 px-2 text-gray-400 hover:bg-[#3E3E3E] hover:text-gray-200"
            onClick={onToggleHints}
          >
            <Info className="h-4 w-4 mr-1" />
            {showHints ? 'Hide Hints' : 'Show Hints'}
          </Button>
        )}
        
        {showPreview ? (
          <Button
            variant="outline"
            size="sm"
            className="h-7 px-2 text-gray-300 hover:bg-[#3E3E3E] hover:text-white border-[#3E3E3E]"
            onClick={onTogglePreview}
          >
            <Code2 className="h-4 w-4 mr-1" />
            Back to Editor
          </Button>
        ) : (
          <Button
            variant="outline"
            size="sm"
            className="h-7 px-2 text-gray-300 hover:bg-[#3E3E3E] hover:text-white border-[#3E3E3E]"
            onClick={onTogglePreview}
          >
            <Play className="h-4 w-4 mr-1" />
            Preview
          </Button>
        )}
      </div>
    </div>
  );
};

export default EditorHeader;
