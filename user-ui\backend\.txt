





Public Routes:
home - '/'
login - '/auth' 
course preview(not purchased state) - '/program/{frontend-development}'




Private Route:
purchased course page - 'learnings/program/{frontend-development}'
module details page - 'learnings/module/{module-id}'
My learnings Page - 'learnings/my-learnings'

Note: Use appropriate HOC for Private routes
and dont use middleware approach for routing







public routes - starts from "/"
 for eg: Home Page
    - endpoint: "/"
    login Page
    - endpoint: "/login"
    course preview
      - endpoint : /program/frontend-development

private routes - starts with /my-learnings 
 for eg: course details
      - endpoint : /my-learnings/program/frontend-development
      
      My Learnings Page
        - endpoint : /my-learnings/learnings


@Suhan
, for JWT auth in Next.js:

Use HTTP-only secure cookies to store JWT (much safer than localStorage).

Set cookie on login (backend):

res.setHeader('Set-Cookie', token=${jwt}; HttpOnly; Secure; Path=/; SameSite=Strict; Max-Age=86400);

Protect /dashboard routes with middleware:

import { NextResponse } from 'next/server';

export function middleware(req) {
 const token = req.cookies.get('token')?.value;
 if (req.nextUrl.pathname.startsWith('/dashboard') && !token) {
   return NextResponse.redirect('/login');
 }
 return NextResponse.next();
}

export const config = { matcher: ['/dashboard/:path*'] };

In API routes, access token via req.cookies.token.

This setup is secure and works with SSR & client.



2 lakh

5 months

frontend development -  (until Dec) (12K * 6 = 72K)
backend development  - (until Dec)  (10K * 2 = 20K)
design (figma)       - 20K
devOps               - 40k

subscription - 2*6 = 12K
90+20+20+20+40+12

content creation - 30K 



after development


25K
25K
15K - 15K
15K - 20K - 15K
15K - 20k
15K - 20K

