const { connectDB, disconnectDB, isConnected } = require('../config/database');
const Course = require('../models/Course');

// Test database connection and basic operations
const testDatabaseConnection = async () => {
  try {
    console.log('🧪 Testing database connection...');
    
    // Connect to database
    await connectDB();
    
    // Test connection status
    console.log('✅ Database connected:', isConnected());
    
    // Test basic CRUD operations
    console.log('\n📝 Testing Course model...');
    
    // Create a test course
    const testCourse = new Course({
      id: 'test-course',
      slug: 'test-course',
      title: 'Test Course',
      description: 'A test course for database testing',
      longDescription: 'This is a comprehensive test course for verifying database functionality',
      badge: 'Test',
      totalLessons: 10,
      level: 'Beginner',
      projects: 2,
      image: 'https://example.com/test-image.jpg',
      color: 'from-red-500 to-pink-500',
      price: 29.99,
      instructor: 'Test Instructor',
      rating: 4.5,
      reviews: 100,
      students: 500,
      lastUpdated: 'December 2023',
      features: ['Test feature 1', 'Test feature 2'],
      topics: ['Test topic 1', 'Test topic 2'],
      resources: [
        { link: 'https://example.com/test-resource.pdf', filename: 'Test Resource.pdf' }
      ]
    });
    
    // Save the test course
    const savedCourse = await testCourse.save();
    console.log('✅ Test course created:', savedCourse.title);
    
    // Test virtual field
    console.log('💰 Formatted price:', savedCourse.formattedPrice);
    
    // Test instance method
    const stats = savedCourse.getStats();
    console.log('📊 Course stats:', stats);
    
    // Test static methods
    const beginnerCourses = await Course.findByLevel('Beginner');
    console.log('🎓 Beginner courses found:', beginnerCourses.length);
    
    const instructorCourses = await Course.findByInstructor('Test Instructor');
    console.log('👨‍🏫 Instructor courses found:', instructorCourses.length);
    
    // Test querying
    const allCourses = await Course.find({ isActive: true });
    console.log('📚 Total active courses:', allCourses.length);
    
    // Clean up - delete test course
    await Course.deleteOne({ id: 'test-course' });
    console.log('🧹 Test course cleaned up');
    
    console.log('\n🎉 All database tests passed!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  } finally {
    // Disconnect from database
    await disconnectDB();
  }
};

// Run test if this file is executed directly
if (require.main === module) {
  testDatabaseConnection();
}

module.exports = { testDatabaseConnection }; 