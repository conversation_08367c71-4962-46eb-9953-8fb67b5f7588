/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  useState,
  useEffect,
  useRef,
  useImperativeHandle,
  forwardRef,
} from "react";
import {
  Play,
  Pause,
  SkipForward,
  Volume2,
  VolumeX,
  Info,
  Maximize2,
  Minimize2,
  Circle,
  Code,
  RotateCcw,
  RotateCw,
  Check,
  Volume,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import clsx from "clsx";

interface KeyMoment {
  id: string;
  timeInSeconds: number;
  challenge: string;
  hints: string[];
  solution: string;
}

interface CourseVideoPlayerProps {
  videoUrl: string;
  keyMoments: KeyMoment[];
  onKeyMomentEncountered: (keyMoment: KeyMoment) => void;
  onComplete: () => void;
  showVideo: boolean;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
  onContinueFromKeyMoment?: () => void;
  resumeVideo?: boolean;
  onOpenEditor?: () => void; // Added prop for opening editor
  initialTime?: number; // New prop for initial video time
  completedKeyMoments?: string[]; // New prop for completed key moments
  skippedKeyMoments?: string[]; // New prop for skipped key moments
  isPlaying?: boolean; // New prop to control playback
  onTogglePlay?: () => void; // New prop to toggle play/pause
}

// Add a type for the ref methods
export interface CourseVideoPlayerHandle {
  getCurrentTime: () => number;
}

const CourseVideoPlayer = forwardRef<
  CourseVideoPlayerHandle,
  CourseVideoPlayerProps
>(
  (
    {
      videoUrl,
      keyMoments,
      onKeyMomentEncountered,
      onComplete,
      showVideo = true,
      isFullscreen = false,
      onToggleFullscreen,
      onContinueFromKeyMoment,
      resumeVideo = false,
      onOpenEditor, // New prop
      initialTime = 0, // New prop
      completedKeyMoments = [], // New prop
      skippedKeyMoments = [], // New prop
      isPlaying = false, // New prop
      onTogglePlay, // New prop
    },
    ref
  ) => {
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(0.8);
    const [isMuted, setIsMuted] = useState(false);
    const [showKeyMomentModal, setShowKeyMomentModal] = useState(false);
    const [currentKeyMoment, setCurrentKeyMoment] = useState<KeyMoment | null>(
      null
    );
    const [videoCompleted, setVideoCompleted] = useState(false);
    const [isHovering, setIsHovering] = useState(false);
    const [lastKeyMomentTime, setLastKeyMomentTime] = useState(0);
    const [loading, setLoading] = useState(false);

    // Add state for dragging
    const [isSeeking, setIsSeeking] = useState(false);
    const [seekPreview, setSeekPreview] = useState<number | null>(null);

    // Add state for skip overlay
    const [skipOverlay, setSkipOverlay] = useState<{
      direction: "back" | "forward";
      seconds: number;
    } | null>(null);

    const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);
    const videoContainerRef = useRef<HTMLDivElement>(null);
    const videoRef = useRef<HTMLVideoElement>(null);

    useEffect(() => {
      // Set initial time if provided
      if (videoRef.current && initialTime > 0) {
        videoRef.current.currentTime = initialTime;
        setCurrentTime(initialTime);
      }
    }, [initialTime, videoUrl]);

    // Effect to handle resumeVideo prop
    useEffect(() => {
      if (resumeVideo && lastKeyMomentTime > 0) {
        if (videoRef.current) {
          videoRef.current.currentTime = lastKeyMomentTime;
          setCurrentTime(lastKeyMomentTime);
        }
      }
    }, [resumeVideo, lastKeyMomentTime]);

    useEffect(() => {
      if (isPlaying) {
        videoRef.current?.play().catch((err) => {
          console.error("Error playing video:", err);
        });

        timerRef.current = setInterval(() => {
          if (videoRef.current) {
            setCurrentTime(videoRef.current.currentTime);

            // Only trigger key moments that are not completed
            const keyMoment = keyMoments.find(
              (km) =>
                Math.abs(km.timeInSeconds - videoRef.current!.currentTime) <
                  1 &&
                lastKeyMomentTime !== km.timeInSeconds &&
                !completedKeyMoments.includes(km.id)
            );

            if (keyMoment) {
              setCurrentKeyMoment(keyMoment);
              setShowKeyMomentModal(true);
              setLastKeyMomentTime(keyMoment.timeInSeconds);
              onKeyMomentEncountered(keyMoment);
            }

            if (
              videoRef.current.currentTime >= videoRef.current.duration &&
              !videoCompleted
            ) {
              setVideoCompleted(true);
              toast.success("Video completed! You've finished the course.");
              onComplete();
            }
          }
        }, 1000);
      } else {
        videoRef.current?.pause();

        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      }

      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      };
    }, [
      isPlaying,
      duration,
      keyMoments,
      onComplete,
      onKeyMomentEncountered,
      videoCompleted,
      lastKeyMomentTime,
      completedKeyMoments,
    ]);

    const togglePlayback = () => {
      if (onTogglePlay) {
        onTogglePlay();
      }
    };

    const handleVolumeChange = (value: number[]) => {
      setVolume(value[0]);
      if (value[0] > 0 && isMuted) {
        setIsMuted(false);
      }
    };

    const toggleMute = () => {
      setIsMuted(!isMuted);
    };

    const formatTime = (seconds: number) => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
    };

    const handleContinue = () => {
      if (currentKeyMoment) {
        onKeyMomentEncountered(currentKeyMoment);
      }
      setShowKeyMomentModal(false);
      if (onContinueFromKeyMoment) {
        onContinueFromKeyMoment();
      } else {
        resumeVideoFromLastKeyMoment();
      }
    };

    const handleSkipChallenge = () => {
      setShowKeyMomentModal(false);
      if (onContinueFromKeyMoment) {
        onContinueFromKeyMoment();
      } else {
        resumeVideoFromLastKeyMoment();
      }
    };

    const resumeVideoFromLastKeyMoment = () => {
      if (videoRef.current && lastKeyMomentTime > 0) {
        videoRef.current.currentTime = lastKeyMomentTime;
        setCurrentTime(lastKeyMomentTime);
      }
    };

    const handleTimeUpdate = () => {
      if (videoRef.current) {
        setCurrentTime(videoRef.current.currentTime);
      }
    };

    const handleVideoEnded = () => {
      if (!videoCompleted) {
        setVideoCompleted(true);
        onComplete();
      }
    };

    // Added method to expose the current key moment time

    useEffect(() => {
      const forceCompleteTimeout = setTimeout(() => {
        if (!videoCompleted && currentTime < duration - 10) {
          setCurrentTime(duration - 5);
        }
      }, 60000);

      return () => clearTimeout(forceCompleteTimeout);
    }, [currentTime, duration, videoCompleted]);

    useEffect(() => {
      if (videoRef.current) {
        videoRef.current.volume = isMuted ? 0 : volume;
      }
    }, [volume, isMuted]);

    // When videoUrl changes, pause and reset video, set loading to true
    useEffect(() => {
      setLoading(true);
      if (videoRef.current) {
        videoRef.current.pause();
        videoRef.current.currentTime = 0;
      }
    }, [videoUrl]);

    // Handler for when video metadata is loaded
    const handleLoadedMetadata = () => {
      if (videoRef.current) {
        setDuration(videoRef.current.duration);
        // If initialTime is set, ensure currentTime is set as well
        if (initialTime > 0) {
          videoRef.current.currentTime = initialTime;
          setCurrentTime(initialTime);
        }
      }
    };

    // Handler for when video is ready
    const handleLoadedData = () => {
      setLoading(false);
    };

    useImperativeHandle(ref, () => ({
      getCurrentTime: () => {
        return videoRef.current?.currentTime || 0;
      },
    }));

    if (!showVideo) {
      return null;
    }

    const sampleVideoUrl =
      "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4";

    // Helper to get seek time from event
    const getSeekTime = (
      e: React.MouseEvent | React.TouchEvent,
      bar: HTMLElement
    ) => {
      let clientX = 0;
      if ("touches" in e && e.touches.length > 0) {
        clientX = e.touches[0].clientX;
      } else if ("clientX" in e) {
        clientX = e.clientX;
      }
      const rect = bar.getBoundingClientRect();
      const x = clientX - rect.left;
      const percent = Math.max(0, Math.min(1, x / rect.width));
      return percent * duration;
    };

    // Helper to show overlay
    const showSkipOverlay = (
      direction: "back" | "forward",
      seconds: number
    ) => {
      setSkipOverlay({ direction, seconds });
      setTimeout(() => setSkipOverlay(null), 200);
    };

    return (
      <div
        className={`relative bg-black overflow-hidden transition-all duration-300 
        ${
          isFullscreen
            ? "fixed inset-0 z-50 w-screen h-screen"
            : "w-full aspect-video rounded-lg"
        }`}
        ref={videoContainerRef}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        <div
          className={`${
            isFullscreen ? "h-full" : "aspect-video"
          } bg-gradient-to-br from-upskilleo-dark-purple to-black relative`}
        >
          <div className="absolute inset-0 flex items-center justify-center">
            <video
              ref={videoRef}
              src={videoUrl || sampleVideoUrl}
              className={`w-full h-full object-cover opacity-95 transition-transform duration-1000 ease-in-out transform hover:scale-105 ${
                isFullscreen ? "object-contain" : "object-cover"
              }`}
              poster="https://images.unsplash.com/photo-1551033406-611cf9a28f67?auto=format&fit=crop&w=800&q=80"
              controls={false}
              playsInline
              muted={isMuted}
              autoPlay={false}
              onLoadedMetadata={handleLoadedMetadata}
              onLoadedData={handleLoadedData}
              onTimeUpdate={handleTimeUpdate}
              onEnded={handleVideoEnded}
              preload="auto"
              onClick={togglePlayback}
            />

            <AnimatePresence>
              {!isPlaying && !showKeyMomentModal && (
                <motion.button
                  key="center-play-btn"
                  initial={{ scale: 0.7, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.7, opacity: 0 }}
                  transition={{ duration: 0.25 }}
                  onClick={togglePlayback}
                  className="absolute  bg-primary/90 hover:bg-primary rounded-full w-16 h-16 flex items-center justify-center transition-all duration-300 hover:scale-110 focus:outline-none"
                  type="button"
                >
                  <Play size={28} fill="white" className="ml-1" />
                </motion.button>
              )}
            </AnimatePresence>
          </div>

          {/* Open Editor Button - Added new fixed button */}
          {onOpenEditor && (
            <Button
              onClick={onOpenEditor}
              className="absolute top-4 right-4 bg-primary hover:bg-primary/90 rounded-md z-10 flex items-center gap-2 shadow-lg"
              size="sm"
            >
              <Code size={16} />
              <span>Open Editor</span>
            </Button>
          )}

          {showKeyMomentModal && currentKeyMoment && (
            <div className="absolute inset-0 bg-black/80 flex items-center justify-center p-8 animate-fade-in max-h-screen overflow-y-auto">
              <div className="bg-card max-w-lg w-full rounded-lg border border-primary/20 p-6 shadow-xl">
                <div className="flex items-center mb-3 text-primary">
                  <Info className="h-5 w-5 mr-2" />
                  <h3 className="text-xl font-bold">Coding Challenge</h3>
                </div>
                <p className="mb-4">{currentKeyMoment.challenge}</p>

                <div className="bg-muted p-3 rounded-md text-sm font-mono mb-4 border-l-2 border-primary">
                  <p>
                    <span className="text-primary font-semibold">Hint:</span>{" "}
                    {currentKeyMoment.hints[0]}
                  </p>
                </div>

                <div className="flex justify-between gap-3 mt-6">
                  <Button
                    variant="outline"
                    onClick={handleSkipChallenge}
                    className="flex-1"
                  >
                    Continue Video
                  </Button>
                  <Button
                    onClick={handleContinue}
                    className="flex-1 relative overflow-hidden group"
                  >
                    <Code className="mr-2 h-4 w-4" />
                    <span>Try Challenge</span>
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Skip overlay */}
          {skipOverlay && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-40">
              <div className="bg-black/70 text-white px-6 py-3 rounded-lg flex items-center gap-3 text-xl font-bold animate-fade-in animate-fade-out">
                {skipOverlay.direction === "back" ? (
                  <RotateCcw size={32} />
                ) : (
                  <RotateCw size={32} />
                )}
                {skipOverlay.direction === "back"
                  ? `- ${skipOverlay.seconds}s`
                  : `+ ${skipOverlay.seconds}s`}
              </div>
            </div>
          )}

          <div
            className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent px-4 py-6 transition-opacity duration-300 ${
              isHovering || !isPlaying ? "opacity-100" : "opacity-0"
            }`}
          >
            <div
              className="relative flex items-center h-8 mb-2 mx-4"
              style={{ cursor: "pointer" }}
              onMouseDown={(e) => {
                const bar = e.currentTarget as HTMLElement;
                setIsSeeking(true);
                const newTime = getSeekTime(e, bar);
                setSeekPreview(newTime);
                setCurrentTime(newTime);
                if (videoRef.current) videoRef.current.currentTime = newTime;
                const onMove = (moveEvent: MouseEvent) => {
                  const fakeEvent = { ...e, clientX: moveEvent.clientX } as any;
                  const t = getSeekTime(fakeEvent, bar);
                  setSeekPreview(t);
                  setCurrentTime(t);
                  if (videoRef.current) videoRef.current.currentTime = t;
                };
                const onUp = (upEvent: MouseEvent) => {
                  setIsSeeking(false);
                  setSeekPreview(null);
                  window.removeEventListener("mousemove", onMove);
                  window.removeEventListener("mouseup", onUp);
                };
                window.addEventListener("mousemove", onMove);
                window.addEventListener("mouseup", onUp);
              }}
              onTouchStart={(e) => {
                const bar = e.currentTarget as HTMLElement;
                setIsSeeking(true);
                const newTime = getSeekTime(e, bar);
                setSeekPreview(newTime);
                setCurrentTime(newTime);
                if (videoRef.current) videoRef.current.currentTime = newTime;
                const onMove = (moveEvent: TouchEvent) => {
                  if (!moveEvent.touches.length) return;
                  const fakeEvent = { ...e, touches: moveEvent.touches } as any;
                  const t = getSeekTime(fakeEvent, bar);
                  setSeekPreview(t);
                  setCurrentTime(t);
                  if (videoRef.current) videoRef.current.currentTime = t;
                };
                const onUp = () => {
                  setIsSeeking(false);
                  setSeekPreview(null);
                  window.removeEventListener("touchmove", onMove);
                  window.removeEventListener("touchend", onUp);
                };
                window.addEventListener("touchmove", onMove);
                window.addEventListener("touchend", onUp);
              }}
            >
              {/* Ultra-slim progress bar background */}
              <div
                className="absolute left-0 right-0 top-1/2 -translate-y-1/2 h-[3px] bg-muted-foreground/30 rounded-full z-0"
              ></div>
              {/* Progress fill */}
              <div
                className="absolute left-0 top-1/2 -translate-y-1/2 h-[3px] bg-primary rounded-full z-10 transition-all duration-300"
                style={{
                  width: `${
                    ((isSeeking && seekPreview !== null
                      ? seekPreview
                      : currentTime) /
                      duration) *
                    100
                  }%`,
                }}
              ></div>
              {/* Breakpoint highlights on the bar */}
              {keyMoments.map((moment) => (
                <div
                  key={moment.id + '-highlight'}
                  className="absolute top-1/2 -translate-y-1/2 h-[7px] rounded-full z-20"
                  style={{
                    left: `calc(${(moment.timeInSeconds / duration) * 100}% - 2px)`,
                    width: '4px',
                    background:
                      skippedKeyMoments.includes(moment.id)
                        ? '#FFA500'
                        : completedKeyMoments.includes(moment.id)
                        ? '#22C55E'
                        : '#3B82F6', // blue-500 for available
                  }}
                ></div>
              ))}

              {/* SVG marker icons above the bar with connector lines, or blue dot on bar if untouched */}
              {keyMoments.map((moment) => {
                const isSkipped = skippedKeyMoments.includes(moment.id);
                const isCompleted = completedKeyMoments.includes(moment.id);
                if (!isSkipped && !isCompleted) {
                  // Render only a blue dot centered on the bar, with tooltip on hover/focus
                  return (
                    <div
                      key={moment.id + '-dot'}
                      className="absolute z-30 group"
                      style={{
                        left: `calc(${(moment.timeInSeconds / duration) * 100}% - 5px)`,
                        top: '50%',
                        transform: 'translateY(-50%)',
                        width: 10,
                        height: 10,
                        pointerEvents: 'auto',
                      }}
                    >
                      <button
                        type="button"
                        className="w-2.5 h-2.5 rounded-full bg-blue-500 border-none outline-none focus:ring-2 focus:ring-primary/40"
                        tabIndex={0}
                        aria-label={moment.challenge}
                        style={{padding: 0, margin: 0}}
                      />
                      {/* Tooltip on hover/focus */}
                      <div className="absolute left-1/2 -translate-x-1/2 -top-8 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 pointer-events-none transition-all duration-150 z-50">
                        <div className="px-3 py-2 rounded-xl bg-black/90 text-white text-xs font-medium shadow-lg flex items-center animate-fade-in">
                          {moment.challenge}
                        </div>
                        <div className="w-2 h-2 bg-black/90 rotate-45 mx-auto -mt-1"></div>
                      </div>
                    </div>
                  );
                }
                // Otherwise, render icon above bar and connector line
                let markerSVG = null;
                let tooltip = "Challenge available";
                let connectorColor = '#3B82F6'; // blue for available
                if (isSkipped) {
                  // Pac-Man style SVG for skipped
                  markerSVG = (
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <circle cx="8" cy="8" r="8" fill="#FFD600" />
                      <path d="M8 8 L16 0 A8 8 0 0 1 8 16 Z" fill="#232323" />
                    </svg>
                  );
                  tooltip = "Challenge skipped";
                  connectorColor = '#FFA500'; // orange
                } else if (isCompleted) {
                  // Flag SVG for completed
                  markerSVG = (
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <rect x="3" y="3" width="2" height="10" rx="1" fill="#22C55E" />
                      <path d="M5 4 L13 5.5 L5 7 V4 Z" fill="#22C55E" />
                    </svg>
                  );
                  tooltip = "Challenge completed";
                  connectorColor = '#22C55E'; // green
                }
                return (
                  <div
                    key={moment.id}
                    className="absolute z-30 flex flex-col items-center group"
                    style={{
                      left: `calc(${(moment.timeInSeconds / duration) * 100}% - 8px)`,
                      top: '-18px',
                      width: 16,
                      pointerEvents: 'auto',
                    }}
                  >
                    <button
                      type="button"
                      className="w-4 h-4 p-0 m-0 bg-transparent border-none outline-none flex items-center justify-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        setCurrentTime(moment.timeInSeconds);
                        if (videoRef.current) {
                          videoRef.current.currentTime = moment.timeInSeconds;
                        }
                      }}
                      tabIndex={0}
                      aria-label={tooltip}
                    >
                      {markerSVG}
                    </button>
                    {/* Connector line from icon to bar */}
                    <div
                      style={{
                        width: '2px',
                        height: '14px',
                        background: connectorColor,
                        marginTop: 0,
                        marginBottom: 0,
                        borderRadius: '1px',
                        zIndex: 10,
                      }}
                    ></div>
                    {/* Minimal tooltip */}
                    <div className="absolute left-1/2 -translate-x-1/2 -top-7 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 pointer-events-none transition-all duration-150 z-50">
                      <div className="px-2 py-1 rounded bg-black/90 text-white text-xs font-medium shadow-md">
                        {tooltip} <span className="text-gray-300 ml-1">{formatTime(moment.timeInSeconds)}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
              {/* Volume icon at far right */}
              <div className="absolute right-0 top-1/2 -translate-y-1/2 z-30" style={{marginRight: -28}}>
                <Volume2 size={16} className="text-muted-foreground/60" />
              </div>
            </div>

            <div className="flex items-center gap-3 flex-wrap px-2 w-full overflow-visible">
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20 h-9 w-9 rounded-full transition-transform hover:scale-110"
                  title="Back 10 seconds"
                  onClick={() => {
                    setCurrentTime((prev) => Math.max(prev - 10, 0));
                    if (videoRef.current) {
                      videoRef.current.currentTime = Math.max(
                        videoRef.current.currentTime - 10,
                        0
                      );
                    }
                    showSkipOverlay("back", 10);
                  }}
                >
                  <RotateCcw size={20} />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20 h-9 w-9 rounded-full transition-transform hover:scale-110"
                  onClick={togglePlayback}
                >
                  <AnimatePresence mode="wait" initial={false}>
                    {isPlaying ? (
                      <motion.span
                        key="pause-bar"
                        initial={{ scale: 0.7, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.7, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Pause size={20} />
                      </motion.span>
                    ) : (
                      <motion.span
                        key="play-bar"
                        initial={{ scale: 0.7, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.7, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Play size={20} />
                      </motion.span>
                    )}
                  </AnimatePresence>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20 h-9 w-9 rounded-full transition-transform hover:scale-110"
                  title="Forward 5 seconds"
                  onClick={() => {
                    setCurrentTime((prev) => Math.min(prev + 5, duration));
                    if (videoRef.current) {
                      videoRef.current.currentTime = Math.min(
                        videoRef.current.currentTime + 5,
                        duration
                      );
                    }
                    showSkipOverlay("forward", 5);
                  }}
                >
                  <RotateCw size={20} />
                </Button>

                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20 h-9 w-9 rounded-full transition-transform hover:scale-110"
                  onClick={() => {
                    setCurrentTime((prev) => Math.min(prev + 10, duration));
                    if (videoRef.current) {
                      videoRef.current.currentTime = Math.min(
                        videoRef.current.currentTime + 10,
                        duration
                      );
                    }
                    if (currentTime + 10 >= duration) {
                      setVideoCompleted(true);
                      onComplete();
                    }
                  }}
                >
                  <SkipForward size={20} />
                </Button>

                <div className="text-white text-sm flex items-center gap-1 min-w-[80px]">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </div>
              </div>

              {/* Removed the extra Slider seek bar here */}

              <div className="flex items-center gap-2 ml-auto">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20 h-9 w-9 rounded-full transition-transform hover:scale-110"
                  onClick={toggleMute}
                >
                  {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
                </Button>

                <div className="w-20 hidden sm:block">
                  <Slider
                    value={[isMuted ? 0 : volume]}
                    min={0}
                    max={1}
                    step={0.01}
                    onValueChange={handleVolumeChange}
                    className="cursor-pointer"
                  />
                </div>

                {onToggleFullscreen && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-white hover:bg-white/20 h-9 w-9 rounded-full transition-transform hover:scale-110"
                    onClick={onToggleFullscreen}
                  >
                    {isFullscreen ? (
                      <Minimize2 size={20} />
                    ) : (
                      <Maximize2 size={20} />
                    )}
                  </Button>
                )}
              </div>
            </div>

            <div className="flex justify-between text-xs text-white/70 mt-1 px-2 sm:hidden">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
          </div>

          {/* Loader overlay while video is loading */}
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/60 z-30">
              <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>
      </div>
    );
  }
);

CourseVideoPlayer.displayName = "CourseVideoPlayer";

export default CourseVideoPlayer;
