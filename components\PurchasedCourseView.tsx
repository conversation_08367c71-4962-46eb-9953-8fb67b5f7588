/* eslint-disable react/no-unescaped-entities */
"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  PlayCircle,
  Award,
  Briefcase,
  FileText,
  CheckCircle,
  User,
  ExternalLink,
  Lock,
  Linkedin,
  Link,
} from "lucide-react";
import { toast } from "sonner";
import { PurchasedCourseProps } from "@/types/course";
import CourseHeader from "./course/CourseHeader";
import CourseProgressOverview from "./course/CourseProgressOverview";
import CurriculumTabs from "./course/CurriculumTabs";
import progressService from "@/services/progressService";
import userService from "@/services/userService";
import { Card, CardContent } from "@/components/ui/card";
import CareerRoadmap from "./course/CareerRoadmap";
import { Progress } from "@/components/ui/progress";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Github } from "lucide-react";
import Navbar from "./Navbar";
import apiService from "@/services/apiService";

const courseKeyMoments = {
  default: [
    {
      id: "1",
      timeInSeconds: 15,
      challenge:
        "Try implementing a function that checks if a number is even or odd.",
      hints: [
        "Use the modulo operator (%) to check if a number is divisible by 2.",
      ],
      solution: "function isEven(num) {\n  return num % 2 === 0;\n}",
    },
    {
      id: "2",
      timeInSeconds: 45,
      challenge: "Write a function to capitalize the first letter of a string.",
      hints: ["Use the string methods charAt(), slice(), and toUpperCase()."],
      solution:
        "function capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}",
    },
  ],
  "html-css": [
    {
      id: "1",
      timeInSeconds: 15,
      challenge: "Create a centered heading with a blue color.",
      hints: [
        "Use the text-align property to center text",
        "Use the color property to change text color",
      ],
      solution:
        '<h1 style="text-align: center; color: blue;">Centered Blue Heading</h1>',
    },
    {
      id: "2",
      timeInSeconds: 30,
      challenge: "Create a simple navigation bar with three links.",
      hints: [
        "Use a <nav> element to contain your navigation",
        "Style the links to remove underlines and add spacing",
      ],
      solution:
        '<nav style="background-color: #333; padding: 10px;">\n  <a href="#" style="color: white; margin-right: 15px; text-decoration: none;">Home</a>\n  <a href="#" style="color: white; margin-right: 15px; text-decoration: none;">About</a>\n  <a href="#" style="color: white; text-decoration: none;">Contact</a>\n</nav>',
    },
    {
      id: "3",
      timeInSeconds: 60,
      challenge: "Build a simple React component that displays a greeting.",
      hints: [
        "Use a functional component with JSX",
        "Pass a name prop to make it dynamic",
      ],
      solution:
        "function Greeting({ name }) {\n  return (\n    <div>\n      <h2>Hello, {name || 'World'}!</h2>\n      <p>Welcome to React</p>\n    </div>\n  );\n}\n\nexport default Greeting;\n\n// In index.jsx:\n// import React from 'react';\n// import ReactDOM from 'react-dom';\n// import Greeting from './Greeting.jsx';\n// \n// ReactDOM.render(<Greeting name=\"User\" />, document.getElementById('root'));",
    },
  ],
};

// Type guard for full course data
function isFullCourseData(data: any): data is {
  title: string;
  description: string;
  image: string;
  modules: any[];
} {
  return (
    data &&
    typeof data.title === "string" &&
    typeof data.description === "string" &&
    typeof data.image === "string" &&
    Array.isArray(data.modules)
  );
}

const PurchasedCourseView: React.FC<PurchasedCourseProps> = ({ courseId }) => {
  const router = useRouter();
  const [overallProgress, setOverallProgress] = useState(0);
  const [courseData, setCourseData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const certificateRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchCourseData = async () => {
      setLoading(true);
      try {
        // Use the canonical user details API for enrolled course details
        const userDetails = await apiService.getUserDetails(courseId);
        const course = userDetails.courseEnrollment?.courseDetails;
        if (course) {
          setCourseData(course);
          // Calculate progress if available and course is full course data
          if (isFullCourseData(course)) {
            const totalSections = course.modules.reduce(
              (total: number, module: any) =>
                total + (module.sections?.length || 0),
              0
            );
            const completedSections = course.modules.reduce(
              (total: number, module: any) =>
                total +
                (module.sections?.filter((s: any) => s.completed).length || 0),
              0
            );
            setOverallProgress(
              totalSections > 0
                ? Math.round((completedSections / totalSections) * 100)
                : 0
            );
          }
        }
      } catch (error) {
        toast.error("Failed to load course data");
      } finally {
        setLoading(false);
      }
    };
    fetchCourseData();
  }, [courseId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        Loading...
      </div>
    );
  }
  if (!courseData || !isFullCourseData(courseData)) {
    // Not enrolled: Show marketing/CTA section
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-upskilleo-purple/10 to-upskilleo-deep-purple/10 p-8">
        <div className="max-w-xl w-full bg-white/90 dark:bg-gray-900/90 rounded-2xl shadow-2xl p-8 flex flex-col items-center text-center border border-upskilleo-purple/10">
          <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple bg-clip-text text-transparent">
            Unlock Your Learning Journey!
          </h2>
          <p className="text-lg text-gray-700 dark:text-gray-200 mb-6">
            Get lifetime access to all modules, hands-on projects, and exclusive
            resources. Join thousands of learners and boost your career with{" "}
            <span className="font-semibold">{courseId}</span>!
          </p>
          <ul className="text-left text-gray-600 dark:text-gray-300 mb-6 space-y-2">
            <li>✅ Full access to all lessons & projects</li>
            <li>✅ Downloadable resources & certificate</li>
            <li>✅ 24/7 community & mentor support</li>
            <li>✅ Career roadmap & portfolio guidance</li>
          </ul>
          <button
            className="bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white px-8 py-3 rounded-lg font-semibold text-lg shadow-lg hover:scale-105 transition-transform"
            onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
          >
            Enroll Now &rarr;
          </button>
          <p className="text-xs text-gray-400 mt-4">
            30-day money-back guarantee. No risk, all reward!
          </p>
        </div>
      </div>
    );
  }

  // At this point, courseData is guaranteed to be full course data
  const fullCourseData = courseData;

  const handleContinue = () => {
    const nextModule = fullCourseData.modules.find(
      (module: any) => module.progress < 100
    );
    if (nextModule) {
      router.push(`/module/${nextModule.id}`);
      toast.success("Continuing your learning journey", {
        description: `Loading ${nextModule.title}`,
      });
    } else {
      router.push(`/module/${fullCourseData.modules[0].id}`);
    }
  };

  const handleShareCertificate = (platform: string) => {
    // Implement share certificate logic based on the platform
    console.log(`Sharing certificate on ${platform}`);
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced background gradients */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Main gradient background with more depth */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-muted/30" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,_var(--tw-gradient-stops))] from-upskilleo-purple/20 via-background/50 to-background" />

        {/* Enhanced animated gradient orbs with more dynamic positioning */}
        <div className="absolute -top-40 -right-40 w-[600px] h-[600px] bg-gradient-to-br from-upskilleo-purple/30 via-upskilleo-deep-purple/20 to-transparent rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-1/2 -left-20 w-[700px] h-[700px] bg-gradient-to-tr from-upskilleo-deep-purple/30 via-upskilleo-purple/20 to-transparent rounded-full blur-3xl animate-pulse delay-700" />
        <div className="absolute -bottom-40 right-1/4 w-[600px] h-[600px] bg-gradient-to-tl from-upskilleo-purple/30 via-upskilleo-deep-purple/20 to-transparent rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/3 left-1/4 w-[400px] h-[400px] bg-gradient-to-br from-upskilleo-deep-purple/25 via-upskilleo-purple/15 to-transparent rounded-full blur-3xl animate-pulse delay-500" />

        {/* Floating gradient particles */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/3 w-32 h-32 bg-gradient-to-br from-upskilleo-purple/20 to-upskilleo-deep-purple/10 rounded-full blur-xl animate-float-slow" />
          <div className="absolute top-2/3 right-1/4 w-40 h-40 bg-gradient-to-bl from-upskilleo-deep-purple/20 to-upskilleo-purple/10 rounded-full blur-xl animate-float-slow delay-300" />
          <div className="absolute bottom-1/4 left-1/2 w-36 h-36 bg-gradient-to-tr from-upskilleo-purple/20 to-upskilleo-deep-purple/10 rounded-full blur-xl animate-float-slow delay-700" />
        </div>

        {/* Enhanced mesh gradient overlay with more depth */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-upskilleo-purple/10 via-transparent to-upskilleo-deep-purple/10 opacity-70" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-upskilleo-purple/15 via-transparent to-transparent" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom,_var(--tw-gradient-stops))] from-upskilleo-deep-purple/15 via-transparent to-transparent" />

        {/* Animated gradient lines with more dynamic effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-upskilleo-purple/30 to-transparent animate-pulse" />
          <div className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-upskilleo-deep-purple/30 to-transparent animate-pulse delay-500" />
          <div className="absolute top-1/2 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-upskilleo-purple/20 to-transparent animate-pulse delay-300" />
          <div className="absolute left-0 top-0 h-full w-[2px] bg-gradient-to-b from-transparent via-upskilleo-purple/20 to-transparent animate-pulse delay-200" />
          <div className="absolute right-0 top-0 h-full w-[2px] bg-gradient-to-b from-transparent via-upskilleo-deep-purple/20 to-transparent animate-pulse delay-700" />
        </div>

        {/* Interactive gradient highlights */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_var(--mouse-x)_var(--mouse-y),_var(--tw-gradient-stops))] from-upskilleo-purple/5 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500" />
      </div>

      {/* Add custom animation keyframes */}
      <style jsx global>{`
        @keyframes float-slow {
          0%,
          100% {
            transform: translateY(0) translateX(0);
          }
          25% {
            transform: translateY(-10px) translateX(5px);
          }
          50% {
            transform: translateY(0) translateX(10px);
          }
          75% {
            transform: translateY(10px) translateX(5px);
          }
        }
        .animate-float-slow {
          animation: float-slow 8s ease-in-out infinite;
        }
      `}</style>

      {/* Content container with enhanced backdrop blur */}
      <div className="relative z-10">
        <Navbar />
        <CourseHeader
          courseTitle={fullCourseData.title}
          courseDescription={fullCourseData.description}
          courseImage={fullCourseData.image}
          modulesCount={fullCourseData.modules?.length || 0}
        />

        <div className="container mx-auto py-8 px-4 md:px-6 -mt-12 md:-mt-16">
          <Card className="border border-primary/10 shadow-lg bg-gradient-to-br from-background/80 via-background/90 to-muted/30 backdrop-blur-sm relative overflow-hidden">
            {/* Card decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-upskilleo-purple/10 via-upskilleo-deep-purple/5 to-transparent rounded-full blur-2xl animate-pulse" />
              <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-upskilleo-deep-purple/10 via-upskilleo-purple/5 to-transparent rounded-full blur-2xl animate-pulse delay-700" />
            </div>

            <CardContent className="p-4 relative">
              <div className="grid md:grid-cols-2 gap-6">
                {/* Course Progress Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center">
                      <PlayCircle className="w-4 h-4 text-white" />
                    </div>
                    Course Progress
                  </h3>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="bg-gradient-to-br from-gray-50/90 to-gray-100/80 dark:from-gray-800/90 dark:to-gray-700/80 backdrop-blur-sm p-3 rounded-lg border border-primary/10 shadow-sm">
                      <p className="text-xs text-muted-foreground">Progress</p>
                      <p className="text-xl font-semibold mt-1 bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple bg-clip-text text-transparent">
                        {overallProgress}%
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Complete
                      </p>
                    </div>
                    <div className="bg-gradient-to-br from-gray-50/90 to-gray-100/80 dark:from-gray-800/90 dark:to-gray-700/80 backdrop-blur-sm p-3 rounded-lg border border-primary/10 shadow-sm">
                      <p className="text-xs text-muted-foreground">Modules</p>
                      <p className="text-xl font-semibold mt-1 bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple bg-clip-text text-transparent">
                        {
                          fullCourseData.modules.filter((m: any) => m.completed)
                            .length
                        }
                        /{fullCourseData.modules.length}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Completed
                      </p>
                    </div>
                    <div className="bg-gradient-to-br from-gray-50/90 to-gray-100/80 dark:from-gray-800/90 dark:to-gray-700/80 backdrop-blur-sm p-3 rounded-lg border border-primary/10 shadow-sm">
                      <p className="text-xs text-muted-foreground">Lessons</p>
                      <p className="text-xl font-semibold mt-1 bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple bg-clip-text text-transparent">
                        {fullCourseData.modules.reduce(
                          (acc: number, m: any) =>
                            acc +
                            (m.sections?.filter((s: any) => s.completed)
                              .length || 0),
                          0
                        )}
                        /
                        {fullCourseData.modules.reduce(
                          (acc: number, m: any) =>
                            acc + (m.sections?.length || 0),
                          0
                        )}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Completed
                      </p>
                    </div>
                  </div>
                </div>

                {/* Success Path Section */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center">
                      <Award className="w-4 h-4 text-white" />
                    </div>
                    Your Success Path
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Card
                          className={`relative overflow-hidden cursor-pointer group hover:shadow-lg hover:scale-[1.02] transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border ${
                            overallProgress >= 100
                              ? "border-blue-200 dark:border-blue-900 hover:border-blue-300 dark:hover:border-blue-800"
                              : "border-gray-200/50 dark:border-gray-700/50 hover:border-gray-300/50 dark:hover:border-gray-600/50"
                          }`}
                        >
                          <div
                            className={`absolute top-0 left-0 right-0 h-0.5 ${
                              overallProgress >= 100
                                ? "bg-gradient-to-r from-blue-500 to-blue-600"
                                : "bg-gray-300/50 dark:bg-gray-600/50"
                            }`}
                          />
                          <div
                            className={`absolute inset-0 bg-gradient-to-br ${
                              overallProgress >= 100
                                ? "from-blue-500/5 to-transparent"
                                : "from-gray-500/5 to-transparent"
                            } opacity-0 group-hover:opacity-100 transition-opacity`}
                          />
                          <CardContent className="p-3 relative">
                            <div className="flex items-center gap-3">
                              <div
                                className={`w-10 h-10 rounded-full ${
                                  overallProgress >= 100
                                    ? "bg-gradient-to-br from-blue-500 to-blue-600 group-hover:from-blue-600 group-hover:to-blue-700"
                                    : "bg-blue-100/40 dark:bg-blue-900/20 group-hover:bg-blue-200/60 dark:group-hover:bg-blue-800/40"
                                } flex items-center justify-center transition-all duration-300 shadow-sm group-hover:shadow-md backdrop-blur-sm`}
                              >
                                <Award
                                  className={`h-5 w-5 ${
                                    overallProgress >= 100
                                      ? "text-white"
                                      : "text-blue-400/70 dark:text-blue-300/70 group-hover:text-blue-500/90 dark:group-hover:text-blue-400/90"
                                  }`}
                                />
                              </div>
                              <div className="flex-1">
                                <h3
                                  className={`font-medium text-sm ${
                                    overallProgress >= 100
                                      ? "text-gray-800 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400"
                                      : "text-gray-500/80 dark:text-gray-400/80 group-hover:text-gray-600 dark:group-hover:text-gray-300"
                                  } transition-colors`}
                                >
                                  Course Certificate
                                </h3>
                                <p className="text-xs text-gray-500/80 dark:text-gray-400/80 group-hover:text-gray-600/90 dark:group-hover:text-gray-300/90">
                                  {overallProgress >= 100
                                    ? "View certificate"
                                    : "Complete course to unlock"}
                                </p>
                              </div>
                              {overallProgress < 100 && (
                                <div className="flex items-center">
                                  <Lock className="h-4 w-4 text-gray-400/70 dark:text-gray-500/70 group-hover:text-gray-500/90 dark:group-hover:text-gray-400/90" />
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-3xl flex flex-col max-h-[90vh]">
                        <DialogHeader>
                          <DialogTitle>Course Certificate</DialogTitle>
                          <DialogDescription>
                            {overallProgress >= 100 ? (
                              "Congratulations! You've earned your certificate."
                            ) : (
                              <span className="text-primary/70">
                                Complete the course to unlock your certificate.
                              </span>
                            )}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="flex-1 overflow-y-auto">
                          <div className="relative overflow-hidden rounded-lg border">
                            <div
                              className={`p-6 ${
                                overallProgress >= 100 ? "" : "blur-sm"
                              }`}
                            >
                              {/* Background gradient effects */}
                              <div className="absolute -top-40 -left-40 w-80 h-80 bg-upskilleo-purple/30 rounded-full blur-3xl"></div>
                              <div className="absolute -bottom-20 -right-20 w-80 h-80 bg-upskilleo-deep-purple/20 rounded-full blur-3xl"></div>

                              <div
                                ref={certificateRef}
                                data-certificate
                                className="relative bg-gradient-to-br from-background to-muted/30 border-8 border-double border-primary/20 p-8 rounded-lg overflow-hidden"
                              >
                                {/* Decorative corner elements */}
                                <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-upskilleo-purple/10 to-transparent rounded-br-full"></div>
                                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-upskilleo-deep-purple/10 to-transparent rounded-bl-full"></div>
                                <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-upskilleo-purple/10 to-transparent rounded-tr-full"></div>
                                <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-upskilleo-deep-purple/10 to-transparent rounded-tl-full"></div>

                                <div className="grid grid-cols-3 gap-8">
                                  {/* Left Column - Award Icon */}
                                  <div className="flex items-center justify-center">
                                    <div className="w-24 h-24 rounded-full bg-gradient-to-br from-upskilleo-purple/20 to-upskilleo-deep-purple/20 flex items-center justify-center shadow-lg">
                                      <Award className="h-12 w-12 text-upskilleo-purple dark:text-upskilleo-deep-purple" />
                                    </div>
                                  </div>

                                  {/* Middle Column - Main Content */}
                                  <div className="col-span-2">
                                    {/* Certificate Header */}
                                    <div className="text-center mb-6 relative">
                                      <h4 className="text-3xl font-serif text-gradient mb-2">
                                        Certificate of Completion
                                      </h4>
                                      <div className="h-px w-40 mx-auto bg-gradient-to-r from-transparent via-primary/50 to-transparent"></div>
                                    </div>

                                    {/* Certificate Body */}
                                    <div className="text-center mb-6 relative">
                                      <p className="text-sm text-muted-foreground mb-3">
                                        This is to certify that
                                      </p>
                                      <p className="text-3xl font-bold text-gradient mb-3">
                                        John Doe
                                      </p>
                                      <p className="text-sm text-muted-foreground mb-3">
                                        has successfully completed the course
                                      </p>
                                      <p className="text-2xl font-semibold text-gradient">
                                        Front-end Web Development
                                      </p>
                                    </div>

                                    {/* Skills Section */}
                                    <div className="mb-6 relative">
                                      <p className="text-sm text-muted-foreground text-center mb-3">
                                        Skills Acquired
                                      </p>
                                      <div className="flex flex-wrap justify-center gap-2">
                                        <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-full bg-gradient-to-r from-blue-500/10 to-blue-600/10 border border-blue-200/20">
                                          <img
                                            src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg"
                                            alt="HTML5"
                                            className="w-4 h-4"
                                          />
                                          <span className="text-xs font-medium text-blue-600">
                                            HTML5
                                          </span>
                                        </div>
                                        <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-full bg-gradient-to-r from-blue-500/10 to-blue-600/10 border border-blue-200/20">
                                          <img
                                            src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/css3/css3-original.svg"
                                            alt="CSS3"
                                            className="w-4 h-4"
                                          />
                                          <span className="text-xs font-medium text-blue-600">
                                            CSS3
                                          </span>
                                        </div>
                                        <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-full bg-gradient-to-r from-yellow-500/10 to-yellow-600/10 border border-yellow-200/20">
                                          <img
                                            src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg"
                                            alt="JavaScript"
                                            className="w-4 h-4"
                                          />
                                          <span className="text-xs font-medium text-yellow-600">
                                            JavaScript
                                          </span>
                                        </div>
                                        <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-full bg-gradient-to-r from-blue-500/10 to-blue-600/10 border border-blue-200/20">
                                          <img
                                            src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg"
                                            alt="React"
                                            className="w-4 h-4"
                                          />
                                          <span className="text-xs font-medium text-blue-600">
                                            React
                                          </span>
                                        </div>
                                        <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-full bg-gradient-to-r from-purple-500/10 to-purple-600/10 border border-purple-200/20">
                                          <img
                                            src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/tailwindcss/tailwindcss-original.svg"
                                            alt="Tailwind"
                                            className="w-4 h-4"
                                          />
                                          <span className="text-xs font-medium text-purple-600">
                                            Tailwind
                                          </span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Certificate Details */}
                                    <div className="grid grid-cols-2 gap-4 mb-4">
                                      <div className="text-center p-3 rounded-lg bg-gradient-to-br from-upskilleo-purple/5 to-transparent">
                                        <p className="text-xs text-muted-foreground mb-1">
                                          Date of Completion
                                        </p>
                                        <p className="font-medium text-gradient">
                                          May 9, 2025
                                        </p>
                                      </div>
                                      <div className="text-center p-3 rounded-lg bg-gradient-to-br from-upskilleo-deep-purple/5 to-transparent">
                                        <p className="text-xs text-muted-foreground mb-1">
                                          Certificate ID
                                        </p>
                                        <p className="font-medium text-gradient">
                                          CERT-FE-12345
                                        </p>
                                      </div>
                                    </div>

                                    {/* Certificate Footer */}
                                    <div className="flex items-center justify-between pt-3 border-t border-primary/10">
                                      <div className="flex items-center gap-2">
                                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple/20 to-upskilleo-deep-purple/20 flex items-center justify-center shadow-md">
                                          <span className="text-xs font-bold text-gradient">
                                            UO
                                          </span>
                                        </div>
                                        <div>
                                          <p className="text-xs font-medium text-gradient">
                                            UpskillEO
                                          </p>
                                          <p className="text-[10px] text-muted-foreground">
                                            Verified Certificate
                                          </p>
                                        </div>
                                      </div>
                                      <div className="text-right">
                                        <p className="text-xs font-medium text-gradient">
                                          Course Instructor
                                        </p>
                                        <p className="text-[10px] text-muted-foreground">
                                          Sarah Johnson
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Certificate Seal */}
                                <div className="absolute bottom-4 right-4 w-20 h-20">
                                  <div className="relative w-full h-full">
                                    {/* Outer circle */}
                                    <div className="absolute inset-0 rounded-full border-4 border-upskilleo-purple/30"></div>
                                    {/* Inner circle */}
                                    <div className="absolute inset-2 rounded-full border-2 border-upskilleo-purple/40"></div>
                                    {/* Center design */}
                                    <div className="absolute inset-4 rounded-full bg-gradient-to-br from-upskilleo-purple/20 to-upskilleo-deep-purple/20 flex items-center justify-center">
                                      <div className="text-center">
                                        <div className="text-xs font-bold text-upskilleo-purple">
                                          UO
                                        </div>
                                        <div className="text-[8px] text-upskilleo-purple/80">
                                          CERTIFIED
                                        </div>
                                      </div>
                                    </div>
                                    {/* Decorative elements */}
                                    <div className="absolute inset-0">
                                      <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1 h-2 bg-upskilleo-purple/30"></div>
                                      <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-1 h-2 bg-upskilleo-purple/30"></div>
                                      <div className="absolute left-0 top-1/2 -translate-y-1/2 w-2 h-1 bg-upskilleo-purple/30"></div>
                                      <div className="absolute right-0 top-1/2 -translate-y-1/2 w-2 h-1 bg-upskilleo-purple/30"></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {overallProgress < 100 && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black/30 backdrop-blur-[2px]">
                                <div className="bg-white dark:bg-gray-800 rounded-full p-2 shadow-lg">
                                  <Lock className="h-6 w-6 text-gray-500" />
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="space-y-3 mt-4">
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="relative">
                                <Progress
                                  value={overallProgress}
                                  className="w-20 h-1.5 bg-secondary/30"
                                />
                                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                                  <div className="absolute top-0 left-0 w-1/2 h-full bg-white/10 transform -skew-x-12 opacity-50 animate-pulse"></div>
                                </div>
                              </div>
                              <p className="text-xs text-gray-500 mt-1">
                                {overallProgress}% Complete
                              </p>
                            </div>
                            {overallProgress >= 100 ? (
                              <Button
                                size="sm"
                                variant="outline"
                                className="flex items-center gap-2"
                              >
                                <ExternalLink className="h-4 w-4" />
                                View Certificate
                              </Button>
                            ) : (
                              <Button
                                size="sm"
                                className="bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white shadow-md hover:scale-105 border-none"
                              >
                                Continue Learning
                              </Button>
                            )}
                          </div>

                          {overallProgress >= 100 && (
                            <div className="border-t pt-3">
                              <p className="text-xs mb-2 text-gray-600 dark:text-gray-300">
                                Share your achievement:
                              </p>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center gap-1.5 h-8 text-xs bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                                  onClick={() =>
                                    handleShareCertificate("LinkedIn")
                                  }
                                >
                                  <Linkedin className="h-3.5 w-3.5" />
                                  LinkedIn
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center gap-1.5 h-8 text-xs"
                                  onClick={() =>
                                    handleShareCertificate("Twitter")
                                  }
                                >
                                  <Link className="h-3.5 w-3.5" />
                                  Other
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      </DialogContent>
                    </Dialog>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Card
                          className={`relative overflow-hidden cursor-pointer group hover:shadow-lg hover:scale-[1.02] transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border ${
                            overallProgress >= 100
                              ? "border-amber-200 dark:border-amber-900 hover:border-amber-300 dark:hover:border-amber-800"
                              : "border-gray-200/50 dark:border-gray-700/50 hover:border-gray-300/50 dark:hover:border-gray-600/50"
                          }`}
                        >
                          <div
                            className={`absolute top-0 left-0 right-0 h-0.5 ${
                              overallProgress >= 100
                                ? "bg-gradient-to-r from-amber-500 to-amber-600"
                                : "bg-gray-300/50 dark:bg-gray-600/50"
                            }`}
                          />
                          <div
                            className={`absolute inset-0 bg-gradient-to-br ${
                              overallProgress >= 100
                                ? "from-amber-500/5 to-transparent"
                                : "from-gray-500/5 to-transparent"
                            } opacity-0 group-hover:opacity-100 transition-opacity`}
                          />
                          <CardContent className="p-3 relative">
                            <div className="flex items-center gap-3">
                              <div
                                className={`w-10 h-10 rounded-full ${
                                  overallProgress >= 100
                                    ? "bg-gradient-to-br from-amber-500 to-amber-600 group-hover:from-amber-600 group-hover:to-amber-700"
                                    : "bg-amber-100/40 dark:bg-amber-900/20 group-hover:bg-amber-200/60 dark:group-hover:bg-amber-800/40"
                                } flex items-center justify-center transition-all duration-300 shadow-sm group-hover:shadow-md backdrop-blur-sm`}
                              >
                                <Briefcase
                                  className={`h-5 w-5 ${
                                    overallProgress >= 100
                                      ? "text-white"
                                      : "text-amber-400/70 dark:text-amber-300/70 group-hover:text-amber-500/90 dark:group-hover:text-amber-400/90"
                                  }`}
                                />
                              </div>
                              <div className="flex-1">
                                <h3
                                  className={`font-medium text-sm ${
                                    overallProgress >= 100
                                      ? "text-gray-800 dark:text-gray-100 group-hover:text-amber-600 dark:group-hover:text-amber-400"
                                      : "text-gray-500/80 dark:text-gray-400/80 group-hover:text-gray-600 dark:group-hover:text-gray-300"
                                  } transition-colors`}
                                >
                                  Internship Opportunity
                                </h3>
                                <p className="text-xs text-gray-500/80 dark:text-gray-400/80 group-hover:text-gray-600/90 dark:group-hover:text-gray-300/90">
                                  {overallProgress >= 100
                                    ? "Apply for positions"
                                    : "Complete course to unlock"}
                                </p>
                              </div>
                              {overallProgress < 100 && (
                                <div className="flex items-center">
                                  <Lock className="h-4 w-4 text-gray-400/70 dark:text-gray-500/70 group-hover:text-gray-500/90 dark:group-hover:text-gray-400/90" />
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      </AlertDialogTrigger>
                      <AlertDialogContent className="max-w-md">
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Internship Opportunities
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            As a top performer in this course, you'll gain
                            exclusive access to our internship network with
                            leading companies.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <div className="py-3">
                          <div className="space-y-2">
                            <div className="bg-white dark:bg-gray-800 border border-amber-200 dark:border-amber-900 rounded-lg p-2.5">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <div className="flex-shrink-0 mr-2.5">
                                    <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900 rounded-md flex items-center justify-center">
                                      <Briefcase className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                                    </div>
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-sm text-gray-800 dark:text-gray-200">
                                      Front-end Developer Intern
                                    </h4>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                      TechCorp Inc. • Remote • 3 months
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  disabled={overallProgress < 100}
                                  className={
                                    overallProgress < 100 ? "opacity-60" : ""
                                  }
                                  aria-label="Apply for front-end developer internship"
                                >
                                  <span className="mr-1 text-xs">Apply</span>
                                  <ExternalLink className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>

                          {overallProgress < 100 && (
                            <div className="mt-3 flex flex-col items-center">
                              <div className="relative w-full">
                                <Progress
                                  value={overallProgress}
                                  className="h-1.5 bg-secondary/30"
                                />
                                <div className="absolute inset-0 overflow-hidden pointer-events-none rounded-full">
                                  <div className="absolute top-0 left-0 w-1/2 h-full bg-white/10 transform -skew-x-12 opacity-50 animate-pulse"></div>
                                </div>
                              </div>
                              <p className="text-xs text-primary mt-1.5">
                                Complete the course to unlock application for
                                these opportunities.
                              </p>
                            </div>
                          )}
                        </div>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="h-8 text-xs">
                            Close
                          </AlertDialogCancel>
                          <AlertDialogAction className="h-8 text-xs">
                            View All Opportunities
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>

                    <Dialog>
                      <DialogTrigger asChild>
                        <Card
                          className={`relative overflow-hidden cursor-pointer group hover:shadow-lg hover:scale-[1.02] transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border ${
                            overallProgress >= 100
                              ? "border-green-200 dark:border-green-900 hover:border-green-300 dark:hover:border-green-800"
                              : "border-gray-200/50 dark:border-gray-700/50 hover:border-gray-300/50 dark:hover:border-gray-600/50"
                          }`}
                        >
                          <div
                            className={`absolute top-0 left-0 right-0 h-0.5 ${
                              overallProgress >= 100
                                ? "bg-gradient-to-r from-green-500 to-green-600"
                                : "bg-gray-300/50 dark:bg-gray-600/50"
                            }`}
                          />
                          <div
                            className={`absolute inset-0 bg-gradient-to-br ${
                              overallProgress >= 100
                                ? "from-green-500/5 to-transparent"
                                : "from-gray-500/5 to-transparent"
                            } opacity-0 group-hover:opacity-100 transition-opacity`}
                          />
                          <CardContent className="p-3 relative">
                            <div className="flex items-center gap-3">
                              <div
                                className={`w-10 h-10 rounded-full ${
                                  overallProgress >= 100
                                    ? "bg-gradient-to-br from-green-500 to-green-600 group-hover:from-green-600 group-hover:to-green-700"
                                    : "bg-green-100/40 dark:bg-green-900/20 group-hover:bg-green-200/60 dark:group-hover:bg-green-800/40"
                                } flex items-center justify-center transition-all duration-300 shadow-sm group-hover:shadow-md backdrop-blur-sm`}
                              >
                                <User
                                  className={`h-5 w-5 ${
                                    overallProgress >= 100
                                      ? "text-white"
                                      : "text-green-400/70 dark:text-green-300/70 group-hover:text-green-500/90 dark:group-hover:text-green-400/90"
                                  }`}
                                />
                              </div>
                              <div className="flex-1">
                                <h3
                                  className={`font-medium text-sm ${
                                    overallProgress >= 100
                                      ? "text-gray-800 dark:text-gray-100 group-hover:text-green-600 dark:group-hover:text-green-400"
                                      : "text-gray-500/80 dark:text-gray-400/80 group-hover:text-gray-600 dark:group-hover:text-gray-300"
                                  } transition-colors`}
                                >
                                  Career Placement
                                </h3>
                                <p className="text-xs text-gray-500/80 dark:text-gray-400/80 group-hover:text-gray-600/90 dark:group-hover:text-gray-300/90">
                                  {overallProgress >= 100
                                    ? "Schedule interviews"
                                    : "Complete course to unlock"}
                                </p>
                              </div>
                              {overallProgress < 100 && (
                                <div className="flex items-center">
                                  <Lock className="h-4 w-4 text-gray-400/70 dark:text-gray-500/70 group-hover:text-gray-500/90 dark:group-hover:text-gray-400/90" />
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                          <DialogTitle>Mock Interview Support</DialogTitle>
                          <DialogDescription>
                            {overallProgress >= 100
                              ? "Schedule a mock interview or career advisory call with our experts."
                              : "Complete the course to unlock mock interviews and career guidance."}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-3">
                          <div className="bg-white dark:bg-gray-800 border rounded-lg p-3">
                            <h3 className="font-medium text-sm mb-2">
                              Mock Interviews
                            </h3>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between p-2 border border-green-100 dark:border-green-900 rounded-md bg-green-50/50 dark:bg-green-950/20">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                    <User className="h-4 w-4 text-green-600 dark:text-green-400" />
                                  </div>
                                  <div>
                                    <p className="font-medium text-sm">
                                      Technical Interview
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      45-60 minutes
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  disabled={overallProgress < 100}
                                  className={
                                    overallProgress < 100 ? "opacity-60" : ""
                                  }
                                >
                                  Schedule
                                </Button>
                              </div>

                              <div className="flex items-center justify-between p-2 border border-green-100 dark:border-green-900 rounded-md bg-green-50/50 dark:bg-green-950/20">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                    <User className="h-4 w-4 text-green-600 dark:text-green-400" />
                                  </div>
                                  <div>
                                    <p className="font-medium text-sm">
                                      Behavioral Interview
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      30-45 minutes
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  disabled={overallProgress < 100}
                                  className={
                                    overallProgress < 100 ? "opacity-60" : ""
                                  }
                                >
                                  Schedule
                                </Button>
                              </div>
                            </div>
                          </div>

                          <div className="bg-white dark:bg-gray-800 border rounded-lg p-3">
                            <h3 className="font-medium text-sm mb-2">
                              Career Expert Calls
                            </h3>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between p-2 border border-blue-100 dark:border-blue-900 rounded-md bg-blue-50/50 dark:bg-blue-950/20">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                    <User className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                  </div>
                                  <div>
                                    <p className="font-medium text-sm">
                                      Resume Review
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      30 minutes
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  disabled={overallProgress < 100}
                                  className={
                                    overallProgress < 100 ? "opacity-60" : ""
                                  }
                                >
                                  Book Call
                                </Button>
                              </div>

                              <div className="flex items-center justify-between p-2 border border-blue-100 dark:border-blue-900 rounded-md bg-blue-50/50 dark:bg-blue-950/20">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                    <User className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                  </div>
                                  <div>
                                    <p className="font-medium text-sm">
                                      Career Strategy
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      45 minutes
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  disabled={overallProgress < 100}
                                  className={
                                    overallProgress < 100 ? "opacity-60" : ""
                                  }
                                >
                                  Book Call
                                </Button>
                              </div>
                            </div>
                          </div>

                          {overallProgress < 100 && (
                            <div className="flex flex-col items-center py-1.5">
                              <div className="relative w-full">
                                <Progress
                                  value={overallProgress}
                                  className="h-1.5 bg-secondary/30"
                                />
                                <div className="absolute inset-0 overflow-hidden pointer-events-none rounded-full">
                                  <div className="absolute top-0 left-0 w-1/2 h-full bg-white/10 transform -skew-x-12 opacity-50 animate-pulse"></div>
                                </div>
                              </div>
                              <p className="text-xs text-primary mt-1.5">
                                Complete the course to unlock these resources
                              </p>
                            </div>
                          )}
                        </div>

                        <DialogFooter>
                          <Button variant="outline" size="sm">
                            Close Preview
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>

                    <Dialog>
                      <DialogTrigger asChild>
                        <Card
                          className={`relative overflow-hidden cursor-pointer group hover:shadow-lg hover:scale-[1.02] transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border ${
                            overallProgress >= 100
                              ? "border-purple-200 dark:border-purple-900 hover:border-purple-300 dark:hover:border-purple-800"
                              : "border-gray-200/50 dark:border-gray-700/50 hover:border-gray-300/50 dark:hover:border-gray-600/50"
                          }`}
                        >
                          <div
                            className={`absolute top-0 left-0 right-0 h-0.5 ${
                              overallProgress >= 100
                                ? "bg-gradient-to-r from-purple-500 to-purple-600"
                                : "bg-gray-300/50 dark:bg-gray-600/50"
                            }`}
                          />
                          <div
                            className={`absolute inset-0 bg-gradient-to-br ${
                              overallProgress >= 100
                                ? "from-purple-500/5 to-transparent"
                                : "from-gray-500/5 to-transparent"
                            } opacity-0 group-hover:opacity-100 transition-opacity`}
                          />
                          <CardContent className="p-3 relative">
                            <div className="flex items-center gap-3">
                              <div
                                className={`w-10 h-10 rounded-full ${
                                  overallProgress >= 100
                                    ? "bg-gradient-to-br from-purple-500 to-purple-600 group-hover:from-purple-600 group-hover:to-purple-700"
                                    : "bg-purple-100/40 dark:bg-purple-900/20 group-hover:bg-purple-200/60 dark:group-hover:bg-purple-800/40"
                                } flex items-center justify-center transition-all duration-300 shadow-sm group-hover:shadow-md backdrop-blur-sm`}
                              >
                                <FileText
                                  className={`h-5 w-5 ${
                                    overallProgress >= 100
                                      ? "text-white"
                                      : "text-purple-400/70 dark:text-purple-300/70 group-hover:text-purple-500/90 dark:group-hover:text-purple-400/90"
                                  }`}
                                />
                              </div>
                              <div className="flex-1">
                                <h3
                                  className={`font-medium text-sm ${
                                    overallProgress >= 100
                                      ? "text-gray-800 dark:text-gray-100 group-hover:text-purple-600 dark:group-hover:text-purple-400"
                                      : "text-gray-500/80 dark:text-gray-400/80 group-hover:text-gray-600 dark:group-hover:text-gray-300"
                                  } transition-colors`}
                                >
                                  UpskillEO Portfolio
                                </h3>
                                <p className="text-xs text-gray-500/80 dark:text-gray-400/80 group-hover:text-gray-600/90 dark:group-hover:text-gray-300/90">
                                  {overallProgress >= 100
                                    ? "View your portfolio"
                                    : "Complete course to unlock"}
                                </p>
                              </div>
                              {overallProgress < 100 && (
                                <div className="flex items-center">
                                  <Lock className="h-4 w-4 text-gray-400/70 dark:text-gray-500/70 group-hover:text-gray-500/90 dark:group-hover:text-gray-400/90" />
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-xl">
                        <DialogHeader>
                          <DialogTitle className="text-lg font-bold">
                            Your UpskillEO Portfolio
                          </DialogTitle>
                          <DialogDescription className="text-xs">
                            A professional showcase of your skills, projects,
                            and achievements.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="border rounded-lg overflow-hidden bg-white dark:bg-gray-800 shadow-lg">
                          {/* Portfolio Header */}
                          <div className="bg-gradient-to-r from-purple-600 to-indigo-600 p-4 text-white">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-16 h-16 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center overflow-hidden border-4 border-white/20 shadow-lg">
                                  <span className="text-xl font-bold text-white">
                                    JD
                                  </span>
                                </div>
                                <div>
                                  <h3 className="font-bold text-lg text-white">
                                    John Doe
                                  </h3>
                                  <p className="text-purple-100 text-sm">
                                    Frontend Developer
                                  </p>
                                  <div className="flex gap-1.5 mt-1.5">
                                    <Badge
                                      variant="secondary"
                                      className="bg-white/20 text-white border-white/30 hover:bg-white/30 text-xs"
                                    >
                                      HTML5
                                    </Badge>
                                    <Badge
                                      variant="secondary"
                                      className="bg-white/20 text-white border-white/30 hover:bg-white/30 text-xs"
                                    >
                                      CSS3
                                    </Badge>
                                    <Badge
                                      variant="secondary"
                                      className="bg-white/20 text-white border-white/30 hover:bg-white/30 text-xs"
                                    >
                                      React
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center gap-1.5">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="rounded-full h-8 w-8 bg-white/10 hover:bg-white/20 text-white"
                                >
                                  <Github className="h-3.5 w-3.5" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="rounded-full h-8 w-8 bg-white/10 hover:bg-white/20 text-white"
                                >
                                  <Linkedin className="h-3.5 w-3.5" />
                                </Button>
                              </div>
                            </div>
                          </div>

                          {/* Portfolio Content */}
                          <div className="p-4 space-y-4">
                            {/* About Section */}
                            <div>
                              <h4 className="font-semibold text-sm mb-2 text-gray-800 dark:text-gray-200">
                                About
                              </h4>
                              <p className="text-xs text-gray-600 dark:text-gray-300 leading-relaxed">
                                Passionate frontend developer with expertise in
                                modern web technologies. Focused on creating
                                responsive, user-friendly interfaces.
                              </p>
                            </div>

                            {/* Certifications Section */}
                            <div>
                              <h4 className="font-semibold text-sm mb-2 text-gray-800 dark:text-gray-200 flex items-center">
                                <Award className="h-4 w-4 mr-1.5 text-purple-600 dark:text-purple-400" />
                                Certifications
                              </h4>
                              <div className="space-y-2">
                                <div className="border border-gray-200 dark:border-gray-700 rounded-md p-3 flex items-center bg-white dark:bg-gray-800 hover:shadow-md transition-all duration-200">
                                  <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-md mr-3">
                                    <Award className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                  </div>
                                  <div className="flex-1">
                                    <p className="font-medium text-sm text-gray-800 dark:text-gray-200">
                                      Front-end Web Development
                                    </p>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                      UpskillEO • Issued May 2025
                                    </p>
                                    <div className="flex gap-1.5 mt-1.5">
                                      <Badge
                                        variant="outline"
                                        className="text-[10px] border-purple-200 text-purple-700 bg-purple-50 dark:border-purple-900 dark:text-purple-300 dark:bg-purple-900/20"
                                      >
                                        HTML
                                      </Badge>
                                      <Badge
                                        variant="outline"
                                        className="text-[10px] border-purple-200 text-purple-700 bg-purple-50 dark:border-purple-900 dark:text-purple-300 dark:bg-purple-900/20"
                                      >
                                        CSS
                                      </Badge>
                                      <Badge
                                        variant="outline"
                                        className="text-[10px] border-purple-200 text-purple-700 bg-purple-50 dark:border-purple-900 dark:text-purple-300 dark:bg-purple-900/20"
                                      >
                                        JavaScript
                                      </Badge>
                                    </div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 w-7 p-0"
                                  >
                                    <ExternalLink className="h-3.5 w-3.5" />
                                  </Button>
                                </div>
                              </div>
                            </div>

                            {/* Projects Section */}
                            <div>
                              <h4 className="font-semibold text-sm mb-2 text-gray-800 dark:text-gray-200 flex items-center">
                                <FileText className="h-4 w-4 mr-1.5 text-purple-600 dark:text-purple-400" />
                                Featured Projects
                              </h4>
                              <div className="grid grid-cols-2 gap-3">
                                <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden bg-white dark:bg-gray-800 hover:shadow-md transition-all duration-200">
                                  <div className="h-28 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 relative overflow-hidden">
                                    <img
                                      src="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b"
                                      alt="Project thumbnail"
                                      className="w-full h-full object-cover"
                                    />
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                                      <div className="p-2 w-full">
                                        <p className="text-white font-medium text-sm">
                                          Personal Portfolio
                                        </p>
                                        <p className="text-white/80 text-xs">
                                          React, Tailwind CSS
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="p-2">
                                    <div className="flex flex-wrap gap-1.5 mb-2">
                                      <Badge
                                        variant="secondary"
                                        className="text-[10px]"
                                      >
                                        React
                                      </Badge>
                                      <Badge
                                        variant="secondary"
                                        className="text-[10px]"
                                      >
                                        Tailwind
                                      </Badge>
                                    </div>
                                    <div className="flex justify-between items-center">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="h-7 text-[10px]"
                                      >
                                        View Live
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-7 w-7 p-0"
                                      >
                                        <Github className="h-3.5 w-3.5" />
                                      </Button>
                                    </div>
                                  </div>
                                </div>

                                <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden bg-white dark:bg-gray-800 hover:shadow-md transition-all duration-200">
                                  <div className="h-28 bg-gradient-to-br from-blue-500/20 to-purple-500/20 relative overflow-hidden">
                                    <img
                                      src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158"
                                      alt="Project thumbnail"
                                      className="w-full h-full object-cover"
                                    />
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                                      <div className="p-2 w-full">
                                        <p className="text-white font-medium text-sm">
                                          E-commerce Dashboard
                                        </p>
                                        <p className="text-white/80 text-xs">
                                          React, Chart.js
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="p-2">
                                    <div className="flex flex-wrap gap-1.5 mb-2">
                                      <Badge
                                        variant="secondary"
                                        className="text-[10px]"
                                      >
                                        React
                                      </Badge>
                                      <Badge
                                        variant="secondary"
                                        className="text-[10px]"
                                      >
                                        Chart.js
                                      </Badge>
                                    </div>
                                    <div className="flex justify-between items-center">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="h-7 text-[10px]"
                                      >
                                        View Live
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-7 w-7 p-0"
                                      >
                                        <Github className="h-3.5 w-3.5" />
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {overallProgress < 100 && (
                          <div className="mt-3 flex flex-col items-center">
                            <div className="relative w-full">
                              <Progress
                                value={overallProgress}
                                className="h-1.5 bg-secondary/30"
                              />
                              <div className="absolute inset-0 overflow-hidden pointer-events-none rounded-full">
                                <div className="absolute top-0 left-0 w-1/2 h-full bg-white/10 transform -skew-x-12 opacity-50 animate-pulse"></div>
                              </div>
                            </div>
                            <p className="text-xs text-primary mt-1.5">
                              Complete the course to unlock your professional
                              portfolio
                            </p>
                          </div>
                        )}

                        <DialogFooter className="mt-4">
                          <Button variant="outline" size="sm">
                            Close Preview
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Curriculum Section */}
          <div className="mt-8">
            <Card className="border border-primary/10 shadow-lg bg-gradient-to-br from-background/80 via-background/90 to-muted/30 backdrop-blur-sm relative overflow-hidden">
              {/* Card decorative elements */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-upskilleo-purple/10 via-upskilleo-deep-purple/5 to-transparent rounded-full blur-2xl animate-pulse" />
                <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-upskilleo-deep-purple/10 via-upskilleo-purple/5 to-transparent rounded-full blur-2xl animate-pulse delay-700" />
              </div>

              <CardContent className="p-6">
                <div className="flex items-center gap-2 mb-6">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center">
                    <FileText className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
                    Course Curriculum
                  </h3>
                </div>

                <CurriculumTabs modules={fullCourseData.modules || []} />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export { courseKeyMoments };
export default PurchasedCourseView;
