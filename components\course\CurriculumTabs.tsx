"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON>, CardHeader, CardContent, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  BookOpen,
  FileText,
  MessageSquare,
  Map,
  Layers,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { CourseModule } from "@/types/course";
import CurriculumQuickView from "./CurriculumQuickView";
import CurriculumDetailedView from "./CurriculumDetailedView";

interface CurriculumTabsProps {
  modules: CourseModule[];
}

const CurriculumTabs: React.FC<CurriculumTabsProps> = ({ modules }) => {
  const [curriculumView, setCurriculumView] = useState<"quick" | "detailed">(
    "quick"
  );
  const [expandedModules, setExpandedModules] = useState<string[]>([]);

  const toggleModuleExpansion = (moduleId: string) => {
    setExpandedModules((prev) =>
      prev.includes(moduleId)
        ? prev.filter((id) => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  const expandAllModules = () => {
    setExpandedModules(modules.map((module) => module.id));
  };

  const collapseAllModules = () => {
    setExpandedModules([]);
  };

  // View change handler (no URL update)
  const handleViewChange = (view: "quick" | "detailed") => {
    setCurriculumView(view);
  };

  return (
    <Tabs
      persistParam="courseTab"
      defaultValue="curriculum"
      className="space-y-4"
    >
      <TabsList className="grid w-full md:w-auto md:inline-grid grid-cols-3 h-auto bg-muted/50 backdrop-blur-sm">
        <TabsTrigger
          value="curriculum"
          className="px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm hover:bg-muted transition-colors"
        >
          <BookOpen className="h-4 w-4 mr-2" />
          <span>Curriculum</span>
        </TabsTrigger>
        <TabsTrigger
          value="resources"
          className="px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm hover:bg-muted transition-colors"
        >
          <FileText className="h-4 w-4 mr-2" />
          <span>Resources</span>
        </TabsTrigger>
        <TabsTrigger
          value="discussions"
          className="px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm hover:bg-muted transition-colors"
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          <span>Discussions</span>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="curriculum" className="animate-in fade-in-50">
        {/* View Toggle Buttons */}
        <div className="mb-6 flex flex-wrap justify-between items-center gap-3">
          <h2 className="text-xl font-semibold">Course Curriculum</h2>
          <div className="flex gap-2">
            <div className="bg-muted/50 backdrop-blur-sm rounded-lg p-1">
              <Button
                variant={curriculumView === "quick" ? "default" : "ghost"}
                size="sm"
                onClick={() => handleViewChange("quick")}
                className={`gap-1.5 cursor-pointer hover:bg-muted/80 transition-colors ${
                  curriculumView === "quick"
                    ? "bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white border-none shadow-md"
                    : ""
                }`}
              >
                <Map className="w-4 h-4" />
                Quick View
              </Button>
              <Button
                variant={curriculumView === "detailed" ? "default" : "ghost"}
                size="sm"
                onClick={() => handleViewChange("detailed")}
                className={`gap-1.5 cursor-pointer hover:bg-muted/80 transition-colors ${
                  curriculumView === "detailed"
                    ? "bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple text-white border-none shadow-md"
                    : ""
                }`}
              >
                <Layers className="w-4 h-4" />
                Detailed View
              </Button>
            </div>

            {curriculumView === "detailed" && (
              <div className="bg-muted/50 backdrop-blur-sm rounded-lg p-1">
                {expandedModules.length < modules.length ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={expandAllModules}
                    className="gap-1.5 cursor-pointer hover:bg-muted/80 transition-colors"
                  >
                    <ChevronDown className="w-4 h-4" />
                    Expand All
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={collapseAllModules}
                    className="gap-1.5 cursor-pointer hover:bg-muted/80 transition-colors"
                  >
                    <ChevronUp className="w-4 h-4" />
                    Collapse All
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Conditional rendering based on selected view */}
        {curriculumView === "quick" ? (
          <CurriculumQuickView modules={modules} />
        ) : (
          <CurriculumDetailedView
            modules={modules}
            expandedModules={expandedModules}
            toggleModuleExpansion={toggleModuleExpansion}
          />
        )}
      </TabsContent>

      <TabsContent value="resources" className="animate-in fade-in-50">
        <Card>
          <CardHeader>
            <CardTitle>Course Resources</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid gap-3">
                {[
                  "Course Slides.pdf",
                  "Example Code Repository",
                  "Cheat Sheet.pdf",
                  "Additional Reading Material",
                ].map((resource, index) => (
                  <div
                    key={index}
                    className="bg-muted/30 p-3 rounded-md flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-primary" />
                      <span>{resource}</span>
                    </div>
                    <Button variant="ghost" size="sm">
                      Download
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="discussions" className="animate-in fade-in-50">
        <Card>
          <CardHeader>
            <CardTitle>Course Discussions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <MessageSquare className="h-10 w-10 mx-auto text-muted-foreground mb-3" />
              <h3 className="text-lg font-medium mb-2">
                Join the conversation
              </h3>
              <p className="text-muted-foreground mb-4">
                Discuss course material with fellow learners and instructors
              </p>
              <Button>Start a Discussion</Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default CurriculumTabs;
