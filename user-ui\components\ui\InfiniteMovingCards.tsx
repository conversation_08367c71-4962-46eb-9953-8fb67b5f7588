"use client";
import React, { useRef } from "react";
import {
  motion,
  useMotionValue,
  useTransform,
  useAnimationFrame,
} from "framer-motion";
import { cn } from "@/lib/utils";

interface InfiniteMovingCardsProps {
  children: React.ReactNode[];
  direction?: "left" | "right";
  speed?: "fast" | "normal" | "slow";
  className?: string;
}

const speedMap = {
  fast: 0.8,
  normal: 1.2,
  slow: 2.5,
};

export default function InfiniteMovingCards({
  children,
  direction = "left",
  speed = "normal",
  className = "",
}: InfiniteMovingCardsProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const x = useMotionValue(0);
  const speedValue = speedMap[speed] || speedMap.normal;
  const [isHovered, setIsHovered] = React.useState(false);

  useAnimationFrame((_t: number, delta: number) => {
    if (isHovered) return;
    const container = containerRef.current;
    if (!container) return;
    const width = container.scrollWidth / 2;
    let newX = x.get();
    if (direction === "left") {
      newX -= (delta / 1000) * speedValue * 60;
      if (Math.abs(newX) >= width) newX = 0;
    } else {
      newX += (delta / 1000) * speedValue * 60;
      if (newX >= width) newX = 0;
    }
    x.set(newX);
  });

  const translateX = useTransform(x, (v: number) => `${v}px`);

  return (
    <div
      ref={containerRef}
      className={cn("relative flex w-full overflow-x-hidden py-2", className)}
      style={{
        maskImage:
          "linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%)",
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <motion.div
        className={cn("flex whitespace-nowrap", className)}
        style={{ x: translateX }}
        aria-hidden="true"
      >
        {children}
        {children}
      </motion.div>
    </div>
  );
}
