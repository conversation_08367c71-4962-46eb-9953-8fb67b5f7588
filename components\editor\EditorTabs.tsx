import React, { useState, useEffect } from 'react';
import { FileText, FileCode, File as FileIcon, Braces, X, Plus } from 'lucide-react';
import { Button } from "@/components/ui/button";

interface FileTab {
  id: string;
  name: string;
  type: string;
  content?: string;
}

interface EditorTabsProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
  tabs: FileTab[];
  onAddTab?: () => void;
  onCloseTab?: (tabId: string) => void;
  allowClosing?: boolean;
  allowAdding?: boolean;
}

const EditorTabs: React.FC<EditorTabsProps> = ({ 
  activeTab, 
  onTabChange,
  tabs,
  onAddTab,
  onCloseTab,
  allowClosing = false,
  allowAdding = false
}) => {
  const [draggingTab, setDraggingTab] = useState<string | null>(null);
  const [tabOrder, setTabOrder] = useState<FileTab[]>(tabs);

  useEffect(() => {
    setTabOrder(tabs);
  }, [tabs]);

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'html':
        return <FileText className="h-4 w-4 mr-1.5" />;
      case 'css':
        return <FileCode className="h-4 w-4 mr-1.5" />;
      case 'js':
        return <FileIcon className="h-4 w-4 mr-1.5" />;
      case 'react':
      case 'jsx':
        return <Braces className="h-4 w-4 mr-1.5" />;
      default:
        return <FileIcon className="h-4 w-4 mr-1.5" />;
    }
  };

  const handleDragStart = (e: React.DragEvent, tabId: string) => {
    setDraggingTab(tabId);
    e.dataTransfer.setData('text/plain', tabId);
    // Create a ghost image (optional)
    const ghostElement = document.createElement('div');
    ghostElement.textContent = tabId;
    ghostElement.style.opacity = '0';
    document.body.appendChild(ghostElement);
    e.dataTransfer.setDragImage(ghostElement, 0, 0);
    setTimeout(() => document.body.removeChild(ghostElement), 0);
  };

  const handleDragOver = (e: React.DragEvent, overTabId: string) => {
    e.preventDefault();
    if (!draggingTab || draggingTab === overTabId) return;

    // Reorder the tabs
    const newOrder = [...tabOrder];
    const draggedTabIndex = newOrder.findIndex(tab => tab.id === draggingTab);
    const dropTabIndex = newOrder.findIndex(tab => tab.id === overTabId);
    
    // Swap positions
    if (draggedTabIndex !== -1 && dropTabIndex !== -1) {
      const [draggedTab] = newOrder.splice(draggedTabIndex, 1);
      newOrder.splice(dropTabIndex, 0, draggedTab);
      setTabOrder(newOrder);
    }
  };

  const handleDragEnd = () => {
    setDraggingTab(null);
  };

  const handleCloseTab = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    if (onCloseTab) {
      onCloseTab(tabId);
    }
  };

  const getTabTypeFromName = (name: string) => {
    const extension = name.split('.').pop()?.toLowerCase();
    if (!extension) return 'file';
    
    switch(extension) {
      case 'html': return 'html';
      case 'css': return 'css'; 
      case 'js': return 'js';
      case 'jsx': case 'tsx': return 'jsx';
      default: return 'file';
    }
  };

  return (
    <div className="flex bg-[#252526] overflow-x-auto border-b border-[#1E1E1E]">
      {tabOrder.map((tab) => (
        <div 
          key={tab.id}
          className="flex items-center relative"
        >
          <button
            draggable
            onDragStart={(e) => handleDragStart(e, tab.id)}
            onDragOver={(e) => handleDragOver(e, tab.id)}
            onDragEnd={handleDragEnd}
            className={`px-3 py-2 text-sm font-medium relative flex items-center cursor-pointer ${
              activeTab === tab.id
                ? 'text-white bg-[#1E1E1E] border-t-2 border-t-upskilleo-purple'
                : 'text-gray-400 hover:text-white hover:bg-[#2D2D2D]'
            }`}
            onClick={() => onTabChange(tab.id)}
          >
            {getFileIcon(getTabTypeFromName(tab.name))}
            {tab.name}
          </button>
          {allowClosing && (
            <button 
              className="h-4 w-4 flex items-center justify-center absolute right-1 top-2 opacity-0 hover:opacity-100 text-gray-400 hover:text-white hover:bg-[#3E3E3E] rounded-sm"
              onClick={(e) => handleCloseTab(e, tab.id)}
            >
              <X className="h-3 w-3" />
            </button>
          )}
        </div>
      ))}
      {allowAdding && onAddTab && (
        <button 
          className="px-2 py-2 text-gray-400 hover:text-white flex items-center"
          onClick={onAddTab}
        >
          <Plus className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

export default EditorTabs;
