import { useCallback, useEffect, useRef } from 'react';

export function useFullscreen<T extends HTMLElement>(onChange?: (isFullscreen: boolean) => void) {
  const elementRef = useRef<T | null>(null);

  // Helper to check if the element is in fullscreen
  const isElementFullscreen = () => {
    if (typeof document === 'undefined') return false;
    return (
      document.fullscreenElement === elementRef.current ||
      // For webkit browsers
      (document as any).webkitFullscreenElement === elementRef.current
    );
  };

  // Request fullscreen for the element
  const enterFullscreen = useCallback(() => {
    const el = elementRef.current;
    if (!el) return;
    if (el.requestFullscreen) {
      el.requestFullscreen();
    } else if ((el as any).webkitRequestFullscreen) {
      (el as any).webkitRequestFullscreen();
    }
  }, []);

  // Exit fullscreen
  const exitFullscreen = useCallback(() => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      (document as any).webkitExitFullscreen();
    }
  }, []);

  // Listen for fullscreen change events
  useEffect(() => {
    const handleChange = () => {
      const isFs = isElementFullscreen();
      if (onChange) onChange(isFs);
    };
    document.addEventListener('fullscreenchange', handleChange);
    document.addEventListener('webkitfullscreenchange', handleChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleChange);
      document.removeEventListener('webkitfullscreenchange', handleChange);
    };
  }, [onChange]);

  return { elementRef, enterFullscreen, exitFullscreen, isElementFullscreen };
} 