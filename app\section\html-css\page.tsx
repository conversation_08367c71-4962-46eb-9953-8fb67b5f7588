'use client';

import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import CourseSectionDetail from '@/components/CourseSectionDetail';

const HTMLCSSPage = () => {
  const courseData = {
    title: "HTML & CSS Foundations",
    description: "Learn the building blocks of web development and how to style websites.",
    modules: [
      {
        id: "module-1",
        title: "HTML Basics",
        description: "Essential HTML elements and structure",
        duration: "37 min",
        lessons: 3,
        completed: false,
        locked: false,
        progress: 33,
        sections: [
          {
            id: "section-1-1",
            title: "Introduction to HTML",
            duration: "10 min",
            completed: true,
            locked: false,
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
            description: "Learn what HTML is and why it's important"
          },
          {
            id: "section-1-2",
            title: "HTML Document Structure",
            duration: "15 min",
            completed: false,
            locked: false,
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
            description: "Understanding the basic structure of HTML documents"
          },
          {
            id: "section-1-3",
            title: "Semantic HTML",
            duration: "12 min",
            completed: false,
            locked: true,
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
            description: "Using HTML elements that convey meaning"
          }
        ]
      },
      {
        id: "module-2",
        title: "CSS Styling",
        description: "How to style HTML elements with CSS",
        duration: "40 min",
        lessons: 3,
        completed: false,
        locked: false,
        progress: 0,
        sections: [
          {
            id: "section-2-1",
            title: "CSS Selectors",
            duration: "8 min",
            completed: false,
            locked: false,
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
            description: "Learn how to select HTML elements to style"
          },
          {
            id: "section-2-2",
            title: "Box Model",
            duration: "14 min",
            completed: false,
            locked: true,
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
            description: "Understanding the CSS box model concept"
          },
          {
            id: "section-2-3",
            title: "Flexbox Layout",
            duration: "18 min",
            completed: false,
            locked: true,
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
            description: "Creating flexible layouts with Flexbox"
          }
        ]
      }
    ]
  };

  return (
    <>
      <Navbar />
      <CourseSectionDetail 
        title={courseData.title}
        description={courseData.description}
        modules={courseData.modules}
      />
      <Footer />
    </>
  );
};

export default HTMLCSSPage; 