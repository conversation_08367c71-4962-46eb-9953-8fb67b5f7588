'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { 
  Eye, 
  EyeOff, 
  LogIn, 
  UserPlus, 
  BookOpen, 
  Check, 
  Star, 
  Users, 
  Award, 
  TrendingUp, 
  Shield,
  Clock,
  Zap,
  Quote
} from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import apiService from '@/services/apiService';
import userService from '@/services/userService';

const Auth = () => {
  const [activeTab, setActiveTab] = useState('login');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  
  // Login form state
  const [loginEmail, setLoginEmail] = useState('<EMAIL>'); // Pre-populate with dummy user
  const [loginPassword, setLoginPassword] = useState('123456'); // Pre-populate with dummy password
  
  // Signup form state
  const [signupName, setSignupName] = useState('');
  const [signupEmail, setSignupEmail] = useState('');
  const [signupPassword, setSignupPassword] = useState('');
  
  const redirectTo = searchParams.get('redirect') || '/';
  const enrollParam = searchParams.get('enroll');

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Handle login form submission
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Use backend API for login
      const response = await apiService.login(loginEmail, loginPassword);
      
      // Store user info using the service
      userService.setUserData({
        id: response.user?.id || '123',
        name: response.user?.name || 'Test User',
        email: loginEmail,
        enrolledCoursesCount: response.user?.enrolledCoursesCount || 0,
        enrolledCourseIds: response.user?.enrolledCourseIds || []
      });
      
      toast.success("Logged in successfully", {
        description: `Welcome back! You have ${response.user?.enrolledCoursesCount || 0} enrolled courses.`,
      });
      
      // Navigate to redirect URL with enroll parameter if it exists
      const finalRedirectUrl = enrollParam === 'true' 
        ? `${redirectTo}?enroll=true`
        : redirectTo;
      router.push(finalRedirectUrl);
    } catch (error: unknown) {
      console.error('Login error:', error);
      const errorMessage = error instanceof Error ? error.message : "Invalid email or password. Please try again.";
      toast.error("Login failed", {
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle signup form submission
  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    if (!signupName || !signupEmail || !signupPassword) {
      toast.error("All fields are required");
      setIsLoading(false);
      return;
    }
    
    try {
      // For now, we'll use the same backend login since we don't have signup endpoint
      // In a real app, you'd call a signup API first
      const response = await apiService.login(signupEmail, signupPassword);
      
      // Store user info using the service
      userService.setUserData({
        id: response.user?.id || `user-${Date.now()}`,
        name: signupName,
        email: signupEmail,
        enrolledCoursesCount: response.user?.enrolledCoursesCount || 0,
        enrolledCourseIds: response.user?.enrolledCourseIds || []
      });
      
      toast.success("Account created successfully", {
        description: `Welcome to Upskilleo, ${signupName}! You have ${response.user?.enrolledCoursesCount || 0} enrolled courses.`,
      });
      
      // Navigate to redirect URL with enroll parameter if it exists
      const finalRedirectUrl = enrollParam === 'true' 
        ? `${redirectTo}?enroll=true`
        : redirectTo;
      router.push(finalRedirectUrl);
    } catch (error: unknown) {
      console.error('Signup error:', error);
      const errorMessage = error instanceof Error ? error.message : "Please try again.";
      toast.error("Account creation failed", {
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-background to-background/95">
      <Navbar />
      <main className="flex-grow flex items-center justify-center p-6 relative">
        {/* Enhanced Background Elements */}
        <div className="absolute inset-0 z-0 pointer-events-none overflow-hidden">
          <div className="absolute -top-20 -right-40 w-[40rem] h-[40rem] bg-gradient-to-br from-upskilleo-purple/10 to-upskilleo-deep-purple/15 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute bottom-0 left-0 w-[30rem] h-[30rem] bg-gradient-to-tr from-upskilleo-purple/10 to-upskilleo-peach/15 rounded-full blur-3xl animate-float-slow" style={{ animationDelay: "1s" }}></div>
          <div className="absolute top-1/3 -left-20 w-64 h-64 border border-upskilleo-purple/20 rounded-full opacity-30 animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-48 h-48 border border-upskilleo-deep-purple/20 rounded-full opacity-40 animate-pulse" style={{ animationDelay: "0.5s" }}></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5 bg-[length:20px_20px]"></div>
        </div>
        
        <div className="w-full max-w-6xl z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center">
            {/* Left Side - Marketing Content */}
            <div className="space-y-6 lg:space-y-8 order-2 lg:order-1">
              {/* Hero Section */}
              <div className="space-y-4 lg:space-y-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                  <div className="inline-block p-2 lg:p-3 bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple rounded-xl lg:rounded-2xl shadow-lg">
                    <BookOpen className="h-6 w-6 lg:h-8 lg:w-8 text-white" />
                  </div>
                  <Badge className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-2 lg:px-3 py-1 lg:py-1.5 text-xs lg:text-sm font-bold shadow-lg w-fit">
                    <div className="flex items-center gap-1 lg:gap-1.5">
                      <TrendingUp className="w-3 h-3" />
                      <span className="hidden sm:inline">TRUSTED BY 50K+ DEVELOPERS</span>
                      <span className="sm:hidden">50K+ DEVELOPERS</span>
                    </div>
                  </Badge>
                </div>
                
                <div className="space-y-3 lg:space-y-4">
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tight leading-tight">
                    <span className="bg-gradient-to-r from-upskilleo-purple via-upskilleo-deep-purple to-upskilleo-peach bg-clip-text text-transparent">
                      Transform Your Career
                    </span>
                    <br />
                    <span className="text-foreground">with Real Skills</span>
                  </h1>
                  <p className="text-base sm:text-lg lg:text-xl text-muted-foreground leading-relaxed">
                    Join thousands of developers who&apos;ve landed their dream jobs with our industry-focused courses and guaranteed internship placement.
                  </p>
                </div>
              </div>

              {/* Social Proof Stats */}
              <div className="grid grid-cols-3 gap-2 lg:gap-4">
                <div className="text-center p-3 lg:p-4 rounded-lg lg:rounded-xl bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 border border-upskilleo-purple/10">
                  <div className="text-lg lg:text-2xl font-bold text-upskilleo-purple">50K+</div>
                  <div className="text-xs lg:text-sm text-muted-foreground">Active Learners</div>
                </div>
                <div className="text-center p-3 lg:p-4 rounded-lg lg:rounded-xl bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 border border-upskilleo-purple/10">
                  <div className="text-lg lg:text-2xl font-bold text-upskilleo-purple">85%</div>
                  <div className="text-xs lg:text-sm text-muted-foreground">Success Rate</div>
                </div>
                <div className="text-center p-3 lg:p-4 rounded-lg lg:rounded-xl bg-gradient-to-br from-upskilleo-purple/5 to-upskilleo-deep-purple/5 border border-upskilleo-purple/10">
                  <div className="text-lg lg:text-2xl font-bold text-upskilleo-purple">200+</div>
                  <div className="text-xs lg:text-sm text-muted-foreground">Partner Companies</div>
                </div>
              </div>

              {/* Benefits */}
              <div className="space-y-3 lg:space-y-4">
                <h3 className="text-base lg:text-lg font-semibold text-foreground">Why Choose Upskilleo?</h3>
                <div className="grid grid-cols-1 gap-2 lg:gap-3">
                  <div className="flex items-start gap-2 lg:gap-3 p-2 lg:p-3 rounded-lg bg-gradient-to-r from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5">
                      <Zap className="w-3 h-3 lg:w-4 lg:h-4 text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-sm lg:text-base text-foreground">Interactive & Impactful Learning</div>
                      <div className="text-xs lg:text-sm text-muted-foreground">Learn by building real projects, not just watching videos</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-2 lg:gap-3 p-2 lg:p-3 rounded-lg bg-gradient-to-r from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5">
                      <Check className="w-3 h-3 lg:w-4 lg:h-4 text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-sm lg:text-base text-foreground">Guaranteed Internship Placement</div>
                      <div className="text-xs lg:text-sm text-muted-foreground">We guarantee you&apos;ll get an internship or your money back</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-2 lg:gap-3 p-2 lg:p-3 rounded-lg bg-gradient-to-r from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5">
                      <Award className="w-3 h-3 lg:w-4 lg:h-4 text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-sm lg:text-base text-foreground">Industry-Recognized Certificates</div>
                      <div className="text-xs lg:text-sm text-muted-foreground">Get certificates trusted by top tech companies</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-2 lg:gap-3 p-2 lg:p-3 rounded-lg bg-gradient-to-r from-upskilleo-purple/5 to-upskilleo-deep-purple/5 hover:from-upskilleo-purple/10 hover:to-upskilleo-deep-purple/10 transition-all">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center flex-shrink-0 mt-0.5">
                      <Users className="w-3 h-3 lg:w-4 lg:h-4 text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-sm lg:text-base text-foreground">1:1 Career Coaching</div>
                      <div className="text-xs lg:text-sm text-muted-foreground">Personal guidance from industry experts</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Testimonial */}
              <div className="relative p-4 lg:p-6 rounded-lg lg:rounded-xl bg-gradient-to-br from-upskilleo-purple/10 to-upskilleo-deep-purple/10 border border-upskilleo-purple/20">
                <Quote className="absolute top-3 left-3 lg:top-4 lg:left-4 w-5 h-5 lg:w-6 lg:h-6 text-upskilleo-purple/50" />
                <p className="text-sm lg:text-lg font-medium text-foreground mb-3 lg:mb-4 pl-6 lg:pl-8">
                  &ldquo;Upskilleo helped me transition from a non-tech background to a full-stack developer role at a top startup. The career support was incredible!&rdquo;
                </p>
                <div className="flex items-center gap-2 lg:gap-3">
                  <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-full bg-gradient-to-br from-upskilleo-purple to-upskilleo-deep-purple flex items-center justify-center text-white text-xs lg:text-sm font-semibold">
                    SK
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm lg:text-base text-foreground truncate">Sarah Kim</div>
                    <div className="text-xs lg:text-sm text-muted-foreground truncate">Full Stack Developer @ TechCorp</div>
                  </div>
                  <div className="flex flex-shrink-0">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 lg:w-4 lg:h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Auth Form */}
            <div className="w-full max-w-md mx-auto order-1 lg:order-2">
              <div className="text-center mb-4 lg:mb-6">
                <h2 className="text-xl lg:text-2xl font-bold tracking-tight">Join the Community</h2>
                <p className="text-sm lg:text-base text-muted-foreground mt-1 lg:mt-2">Start your journey to a better career</p>
              </div>
              
              <Card className="border-upskilleo-purple/20 shadow-xl bg-background/80 backdrop-blur-sm">
                <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
                  <CardHeader className="pb-3 lg:pb-4">
                    <TabsList className="grid grid-cols-2 w-full bg-upskilleo-soft-purple/20 h-10 lg:h-11">
                      <TabsTrigger value="login" className="data-[state=active]:bg-upskilleo-purple data-[state=active]:text-white text-sm lg:text-base">
                        <LogIn className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
                        <span className="hidden sm:inline">Login</span>
                        <span className="sm:hidden">Sign In</span>
                      </TabsTrigger>
                      <TabsTrigger value="signup" className="data-[state=active]:bg-upskilleo-purple data-[state=active]:text-white text-sm lg:text-base">
                        <UserPlus className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
                        <span className="hidden sm:inline">Sign Up</span>
                        <span className="sm:hidden">Sign Up</span>
                      </TabsTrigger>
                    </TabsList>
                  </CardHeader>
                
                  <CardContent className="px-4 lg:px-6">
                    <TabsContent value="login" className="mt-0">
                      <form onSubmit={handleLogin}>
                        <div className="space-y-3 lg:space-y-4">
                          <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="email" className="text-sm lg:text-base">Email</Label>
                            <Input 
                              id="email" 
                              type="email" 
                              placeholder="<EMAIL>" 
                              value={loginEmail}
                              onChange={(e) => setLoginEmail(e.target.value)}
                              className="border-upskilleo-purple/20 focus:border-upskilleo-purple h-10 lg:h-11 text-sm lg:text-base"
                              required 
                            />
                          </div>
                          <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="password" className="text-sm lg:text-base">Password</Label>
                            <div className="relative">
                              <Input 
                                id="password" 
                                type={showPassword ? "text" : "password"} 
                                placeholder="••••••••" 
                                value={loginPassword}
                                onChange={(e) => setLoginPassword(e.target.value)}
                                className="border-upskilleo-purple/20 focus:border-upskilleo-purple pr-10 h-10 lg:h-11 text-sm lg:text-base"
                                required 
                              />
                              <Button 
                                type="button"
                                variant="ghost" 
                                size="icon" 
                                onClick={togglePasswordVisibility}
                                className="absolute right-0 top-0 h-full hover:bg-upskilleo-soft-purple w-10 lg:w-11"
                              >
                                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                              </Button>
                            </div>
                          </div>
                        </div>
                        
                        <Button type="submit" className="w-full mt-4 lg:mt-6 bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple hover:from-upskilleo-purple/90 hover:to-upskilleo-deep-purple/90 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 h-10 lg:h-11 text-sm lg:text-base" disabled={isLoading}>
                          <LogIn className="mr-2 h-3 w-3 lg:h-4 lg:w-4" />
                          Login
                        </Button>
                      </form>
                    </TabsContent>
                    
                    <TabsContent value="signup" className="mt-0">
                      <form onSubmit={handleSignup}>
                        <div className="space-y-3 lg:space-y-4">
                          <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="name" className="text-sm lg:text-base">Full Name</Label>
                            <Input 
                              id="name" 
                              placeholder="John Doe" 
                              value={signupName}
                              onChange={(e) => setSignupName(e.target.value)}
                              className="border-upskilleo-purple/20 focus:border-upskilleo-purple h-10 lg:h-11 text-sm lg:text-base"
                              required 
                            />
                          </div>
                          <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="signup-email" className="text-sm lg:text-base">Email</Label>
                            <Input 
                              id="signup-email" 
                              type="email" 
                              placeholder="<EMAIL>" 
                              value={signupEmail}
                              onChange={(e) => setSignupEmail(e.target.value)}
                              className="border-upskilleo-purple/20 focus:border-upskilleo-purple h-10 lg:h-11 text-sm lg:text-base"
                              required 
                            />
                          </div>
                          <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="signup-password" className="text-sm lg:text-base">Password</Label>
                            <div className="relative">
                              <Input 
                                id="signup-password" 
                                type={showPassword ? "text" : "password"} 
                                placeholder="••••••••" 
                                value={signupPassword}
                                onChange={(e) => setSignupPassword(e.target.value)}
                                className="border-upskilleo-purple/20 focus:border-upskilleo-purple pr-10 h-10 lg:h-11 text-sm lg:text-base"
                                required 
                              />
                              <Button 
                                type="button"
                                variant="ghost" 
                                size="icon" 
                                onClick={togglePasswordVisibility}
                                className="absolute right-0 top-0 h-full hover:bg-upskilleo-soft-purple w-10 lg:w-11"
                              >
                                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                              </Button>
                            </div>
                          </div>
                        </div>
                        
                        <Button type="submit" className="w-full mt-4 lg:mt-6 bg-gradient-to-r from-upskilleo-purple to-upskilleo-deep-purple hover:from-upskilleo-purple/90 hover:to-upskilleo-deep-purple/90 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 h-10 lg:h-11 text-sm lg:text-base" disabled={isLoading}>
                          <UserPlus className="mr-2 h-3 w-3 lg:h-4 lg:w-4" />
                          Create Account
                        </Button>
                      </form>
                    </TabsContent>
                  </CardContent>
                  
                  <CardFooter className="flex justify-center border-t border-upskilleo-purple/10 p-3 lg:p-4">
                    <p className="text-xs lg:text-sm text-muted-foreground">
                      {activeTab === 'login' 
                        ? "Don't have an account? " 
                        : "Already have an account? "}
                      <button 
                        type="button" 
                        className="text-upskilleo-purple hover:text-upskilleo-deep-purple hover:underline font-medium"
                        onClick={() => setActiveTab(activeTab === 'login' ? 'signup' : 'login')}
                      >
                        {activeTab === 'login' ? 'Sign up' : 'Login'}
                      </button>
                    </p>
                  </CardFooter>
                </Tabs>

                {/* Trust Indicators */}
                <div className="p-3 lg:p-4 border-t border-upskilleo-purple/10">
                  <div className="flex items-center justify-center gap-2 lg:gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Shield className="w-3 h-3" />
                      <span>Secure</span>
                    </div>
                    <div className="w-1 h-1 rounded-full bg-muted"></div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>30-day guarantee</span>
                    </div>
                    <div className="w-1 h-1 rounded-full bg-muted"></div>
                    <div className="flex items-center gap-1">
                      <Zap className="w-3 h-3" />
                      <span>Instant access</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Auth;
