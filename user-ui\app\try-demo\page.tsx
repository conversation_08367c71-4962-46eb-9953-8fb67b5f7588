"use client";
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Play,
  Code,
  MessageSquare,
  CheckCircle,
  Lightbulb,
  Video,
  Book,
  ChevronRight,
} from "lucide-react";
import CourseVideoPlayer from "@/components/CourseVideoPlayer";
import CourseEditor from "@/components/CourseEditor";
import CourseFeedback from "@/components/CourseFeedback";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { toast } from "sonner";

// Move keyMoments outside the component to prevent them from resetting on remounts
const keyMoments = [
  {
    id: "1",
    timeInSeconds: 15,
    challenge:
      "Try implementing a function that checks if a number is even or odd.",
    hints: [
      "Use the modulo operator (%) to check if a number is divisible by 2.",
    ],
    solution: "function isEven(num) {\n  return num % 2 === 0;\n}",
  },
  {
    id: "2",
    timeInSeconds: 45,
    challenge: "Write a function to capitalize the first letter of a string.",
    hints: ["Use the string methods charAt(), slice(), and toUpperCase()."],
    solution:
      "function capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}",
  },
  {
    id: "3",
    timeInSeconds: 75,
    challenge:
      "Create a function that filters out negative numbers from an array.",
    hints: ["Use the array filter() method with a callback function."],
    solution:
      "function filterPositive(numbers) {\n  return numbers.filter(num => num >= 0);\n}",
  },
];

const TryDemo = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [showEditor, setShowEditor] = useState(false);
  const [currentKeyMoment, setCurrentKeyMoment] = useState(null);
  const [code, setCode] = useState(
    "// Write your solution here\nfunction isEven(num) {\n  // Your code here\n}"
  );
  const [showFeedback, setShowFeedback] = useState(false);
  const [activeTab, setActiveTab] = useState("demo");
  const [showChallengeModal, setShowChallengeModal] = useState(false);
  const [resumeVideo, setResumeVideo] = useState(false);
  const [demoCompleted, setDemoCompleted] = useState(false);
  const [lastKeyMomentTime, setLastKeyMomentTime] = useState(0);

  const currentVideoKey = useRef("demo-video");
  const isMobile = useIsMobile();

  const maxSteps = 5;
  const progress = (currentStep / maxSteps) * 100;

  const sampleVideoUrl =
    "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4";

  useEffect(() => {
    const completeTimeout = setTimeout(() => {
      if (!demoCompleted) {
        setDemoCompleted(true);
        setCurrentStep(maxSteps);
        toast.success(
          "Demo completed! Great job exploring our interactive learning platform."
        );
      }
    }, 180000); // 3 minutes max for the entire demo

    return () => clearTimeout(completeTimeout);
  }, [demoCompleted]);

  const handleKeyMomentEncountered = (keyMoment) => {
    setCurrentKeyMoment(keyMoment);
    setLastKeyMomentTime(keyMoment.timeInSeconds);

    if (keyMoment.id === "1") {
      setCode(
        "// Write your solution here\nfunction isEven(num) {\n  // Your code here\n}"
      );
    } else if (keyMoment.id === "2") {
      setCode(
        "// Write your solution here\nfunction capitalize(str) {\n  // Your code here\n}"
      );
    } else if (keyMoment.id === "3") {
      setCode(
        "// Write your solution here\nfunction filterPositive(numbers) {\n  // Your code here\n}"
      );
    }

    setShowChallengeModal(true);
    setResumeVideo(false);
  };

  const handleStartCoding = () => {
    setShowChallengeModal(false);
    setShowEditor(true);
    setCurrentStep(3);
  };

  const handleResetCode = () => {
    if (currentKeyMoment?.id === "1") {
      setCode(
        "// Write your solution here\nfunction isEven(num) {\n  // Your code here\n}"
      );
    } else if (currentKeyMoment?.id === "2") {
      setCode(
        "// Write your solution here\nfunction capitalize(str) {\n  // Your code here\n}"
      );
    } else if (currentKeyMoment?.id === "3") {
      setCode(
        "// Write your solution here\nfunction filterPositive(numbers) {\n  // Your code here\n}"
      );
    }
  };

  const handleSubmitCode = () => {
    setShowFeedback(true);
    const isCorrect = code.includes(
      currentKeyMoment?.id === "1"
        ? "return num % 2 === 0"
        : currentKeyMoment?.id === "2"
        ? "return str.charAt(0).toUpperCase() + str.slice(1)"
        : "return numbers.filter(num => num >= 0)"
    );

    if (isCorrect) {
      toast.success("Great job! Your solution is correct!");
      setTimeout(() => {
        handleContinue();
      }, 5000);
    } else {
      toast.info("Try again! Your solution needs some work.");
    }
  };

  const handleContinue = () => {
    setShowFeedback(false);
    setShowEditor(false);
    setResumeVideo(true);
    setCurrentStep((prev) => Math.min(prev + 1, maxSteps));

    if (currentKeyMoment?.id === "3" || currentStep >= maxSteps - 1) {
      setTimeout(() => {
        setDemoCompleted(true);
        setCurrentStep(maxSteps);
        toast.success(
          "Demo completed! You've experienced our interactive learning platform."
        );
      }, 5000);
    }
  };

  const handleSkipChallenge = () => {
    toast.info("Challenge skipped. Moving on to the next part.");
    setShowChallengeModal(false);
    setShowEditor(false);
    setResumeVideo(true);
    setCurrentStep((prev) => Math.min(prev + 1, maxSteps));
  };

  const handleResumeVideo = () => {
    setShowChallengeModal(false);
    setShowEditor(false);
    setResumeVideo(true);
    setCurrentStep(1);
  };

  const handleVideoComplete = () => {
    toast.success(
      "Video lesson completed! You've experienced our interactive learning approach."
    );
    setDemoCompleted(true);
    setCurrentStep(maxSteps);
  };

  const ChallengeModal = () => {
    const Comp = isMobile ? Drawer : Dialog;
    const Content = isMobile ? DrawerContent : DialogContent;
    const Header = isMobile ? DrawerHeader : DialogHeader;
    const Footer = isMobile ? DrawerFooter : DialogFooter;
    const Title = isMobile ? DrawerTitle : DialogTitle;
    const Description = isMobile ? DrawerDescription : DialogDescription;

    return (
      <Comp open={showChallengeModal} onOpenChange={setShowChallengeModal}>
        <Content className="sm:max-w-[500px]">
          <Header>
            <Title>Coding Challenge</Title>
            <Description>
              The video has paused at a key learning moment. Try to implement
              the solution:
            </Description>
          </Header>
          <div className="p-4 border rounded-md my-4 bg-card">
            <p className="font-medium mb-2">{currentKeyMoment?.challenge}</p>
            <div className="bg-muted/50 p-3 rounded text-sm mt-4">
              <p className="font-semibold text-primary">Hint:</p>
              <p>{currentKeyMoment?.hints[0]}</p>
            </div>
          </div>
          <Footer>
            <Button variant="outline" onClick={handleResumeVideo}>
              Skip for now
            </Button>
            <Button onClick={handleStartCoding}>
              <Code className="mr-2 h-4 w-4" /> Start Coding
            </Button>
          </Footer>
        </Content>
      </Comp>
    );
  };

  const steps = [
    {
      title: "Video Lesson Plays",
      description: "The learner watches an instructor explain a concept.",
      icon: <Video className="h-10 w-10 text-primary" />,
      content: (
        <div className="bg-muted/50 rounded-lg p-6 border">
          <h3 className="font-semibold text-lg mb-2">
            Understanding JavaScript Fundamentals
          </h3>
          <p className="text-muted-foreground mb-4">
            In this lesson, we'll learn essential JavaScript functions and
            methods for everyday coding tasks.
          </p>
          {!showEditor && (
            <CourseVideoPlayer
              videoUrl={sampleVideoUrl}
              keyMoments={keyMoments}
              onKeyMomentEncountered={handleKeyMomentEncountered}
              onComplete={handleVideoComplete}
              showVideo={!showEditor}
            />
          )}
          {showEditor && (
            <div className="mt-6 animate-fade-in">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-lg">Your Challenge</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleResumeVideo}
                  className="flex items-center gap-1.5 text-primary"
                >
                  <Video className="h-4 w-4" /> Back to Video
                </Button>
              </div>
              <CourseEditor
                code={code}
                onChange={setCode}
                language="javascript"
                hints={currentKeyMoment?.hints || []}
                onSkip={handleSkipChallenge}
                onSubmit={handleSubmitCode}
                onReset={handleResetCode}
                initialCode={
                  currentKeyMoment?.id === "1"
                    ? "// Write your solution here\nfunction isEven(num) {\n  // Your code here\n}"
                    : currentKeyMoment?.id === "2"
                    ? "// Write your solution here\nfunction capitalize(str) {\n  // Your code here\n}"
                    : "// Write your solution here\nfunction filterPositive(numbers) {\n  // Your code here\n}"
                }
              />
              {!showFeedback ? (
                <div className="flex justify-between mt-4">
                  <Button variant="outline" onClick={handleSkipChallenge}>
                    Skip Challenge
                  </Button>
                  <Button onClick={handleSubmitCode}>
                    Submit Solution <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div className="mt-4">
                  <CourseFeedback
                    code={code}
                    expectedSolution={
                      currentKeyMoment?.id === "1"
                        ? "return num % 2 === 0"
                        : currentKeyMoment?.id === "2"
                        ? "return str.charAt(0).toUpperCase() + str.slice(1)"
                        : "return numbers.filter(num => num >= 0)"
                    }
                    onClose={handleContinue}
                    onSkip={handleSkipChallenge}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Auto-Pause & Interactive Exercise",
      description:
        "After explaining a key topic, the video pauses automatically, and a hands-on coding challenge appears.",
      icon: <Code className="h-10 w-10 text-primary" />,
      content: (
        <div className="bg-muted/50 rounded-lg p-6 border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-lg">Your Challenge</h3>
            <span className="text-xs bg-primary/20 text-primary px-2 py-1 rounded-full">
              JavaScript
            </span>
          </div>
          <div className="p-4 bg-card rounded-md border mb-4">
            <p className="mb-2 font-medium">
              Implement a function that checks if a number is even or odd.
            </p>
            <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
              <li>
                Function name should be{" "}
                <code className="bg-muted px-1 py-0.5 rounded">isEven</code>
              </li>
              <li>
                It should take one parameter:{" "}
                <code className="bg-muted px-1 py-0.5 rounded">num</code>
              </li>
              <li>
                Return{" "}
                <code className="bg-muted px-1 py-0.5 rounded">true</code> if
                the number is even,{" "}
                <code className="bg-muted px-1 py-0.5 rounded">false</code>{" "}
                otherwise
              </li>
            </ul>
          </div>
          <Button
            onClick={() => {
              setShowEditor(true);
              setCurrentStep(3);
            }}
            className="w-full"
          >
            <Code className="mr-2" /> Start Coding
          </Button>
        </div>
      ),
    },
    {
      title: "Live Coding Execution",
      description:
        "The learner writes and runs code inside an embedded Monaco Editor (for frontend) or WebContainer (for Node.js backend).",
      icon: <Code className="h-10 w-10 text-primary" />,
      content: (
        <div className="bg-muted/50 rounded-lg p-6 border">
          <h3 className="font-semibold text-lg mb-2">Write Your Solution</h3>
          <CourseEditor code={code} onChange={setCode} language="javascript" />
          <div className="flex justify-end mt-4">
            <Button onClick={handleSubmitCode}>Submit Solution</Button>
          </div>
        </div>
      ),
    },
    {
      title: "Instant Feedback",
      description:
        "AI or predefined test cases evaluate the learner's code and provide feedback.",
      icon: <MessageSquare className="h-10 w-10 text-primary" />,
      content: (
        <div className="bg-muted/50 rounded-lg p-6 border">
          <h3 className="font-semibold text-lg mb-4">Code Feedback</h3>
          <CourseFeedback
            code={code}
            expectedSolution="return num % 2 === 0"
            onClose={handleContinue}
          />
        </div>
      ),
    },
    {
      title: "Resume Video or Retry",
      description:
        "If correct, the learner continues watching. If incorrect, they get hints or retry the task.",
      icon: <CheckCircle className="h-10 w-10 text-primary" />,
      content: (
        <div className="bg-muted/50 rounded-lg p-6 border">
          <div className="mb-6 text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="font-semibold text-xl mb-2">Great Job!</h3>
            <p className="text-muted-foreground">
              You've successfully completed this part of the lesson.
            </p>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Button variant="outline" onClick={() => setCurrentStep(3)}>
              <Code className="mr-2" /> Try Another Challenge
            </Button>
            <Button onClick={() => navigate("/")}>
              <Video className="mr-2" /> Continue Learning
            </Button>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/30">
      <div className="container mx-auto py-6 px-4 md:px-6">
        <Button variant="ghost" className="mb-6" onClick={() => navigate("/")}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Home
        </Button>

        <div className="mb-10">
          <h1 className="text-3xl font-bold mb-2">Interactive Learning Demo</h1>
          <p className="text-muted-foreground max-w-2xl">
            Experience our unique learning approach with this interactive demo
            showcasing how Upskilleo courses blend video lessons with hands-on
            coding challenges.
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="demo">Interactive Demo</TabsTrigger>
            <TabsTrigger value="how">How It Works</TabsTrigger>
          </TabsList>

          <TabsContent value="demo" className="mt-6">
            <div className="mb-8">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium">Your Progress</h3>
                <span className="text-sm text-muted-foreground">
                  Step {currentStep} of {maxSteps}
                </span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            <div className="grid md:grid-cols-12 gap-6">
              <div className="md:col-span-4 lg:col-span-3">
                <div className="bg-card rounded-lg border p-4">
                  <h3 className="font-semibold mb-4">Learning Process</h3>
                  <ol className="space-y-6 relative">
                    {steps.map((step, index) => {
                      const isActive = index + 1 === currentStep;
                      const isCompleted = index + 1 < currentStep;

                      return (
                        <li
                          key={index}
                          className={`pl-8 relative ${
                            isActive
                              ? "text-primary font-medium"
                              : isCompleted
                              ? "text-muted-foreground"
                              : "text-muted-foreground/70"
                          }`}
                        >
                          {index < steps.length - 1 && (
                            <div
                              className={`absolute left-4 top-7 w-0.5 h-full -ml-px ${
                                isCompleted ? "bg-primary" : "bg-muted"
                              }`}
                            ></div>
                          )}

                          <div
                            className={`absolute left-0 top-0.5 rounded-full w-8 h-8 flex items-center justify-center
                              ${
                                isActive
                                  ? "bg-primary text-primary-foreground"
                                  : isCompleted
                                  ? "bg-primary/20 text-primary"
                                  : "bg-muted text-muted-foreground"
                              }`}
                          >
                            {isCompleted ? (
                              <CheckCircle className="h-4 w-4" />
                            ) : (
                              index + 1
                            )}
                          </div>

                          <p className="font-medium">{step.title}</p>
                          <p className="text-xs mt-1">{step.description}</p>

                          {isActive && (
                            <div className="mt-2">
                              <Button
                                variant="link"
                                className="p-0 h-auto text-xs"
                                onClick={() =>
                                  index < steps.length - 1 &&
                                  setCurrentStep(index + 2)
                                }
                              >
                                Skip to next step →
                              </Button>
                            </div>
                          )}
                        </li>
                      );
                    })}
                  </ol>
                </div>
              </div>

              <div className="md:col-span-8 lg:col-span-9">
                {demoCompleted ? (
                  <div className="bg-muted/50 rounded-lg p-6 border animate-in fade-in-50">
                    <div className="mb-6 text-center">
                      <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                      <h3 className="font-semibold text-xl mb-2">
                        Demo Completed!
                      </h3>
                      <p className="text-muted-foreground max-w-lg mx-auto">
                        You've experienced our interactive learning platform.
                        This is how our courses combine video lessons with
                        hands-on coding challenges to help you learn more
                        effectively.
                      </p>
                    </div>
                    <div className="grid sm:grid-cols-2 gap-4 max-w-md mx-auto">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setDemoCompleted(false);
                          setCurrentStep(1);
                          setShowEditor(false);
                          setResumeVideo(false);
                          setLastKeyMomentTime(0);
                        }}
                      >
                        <Play className="mr-2 h-4 w-4" /> Restart Demo
                      </Button>
                      <Button onClick={() => navigate("/")}>
                        <Book className="mr-2 h-4 w-4" /> Explore Courses
                      </Button>
                    </div>
                  </div>
                ) : showEditor ? (
                  <div className="animate-in fade-in-50 bg-muted/50 rounded-lg p-6 border">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold text-lg">Your Challenge</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleResumeVideo}
                        className="flex items-center gap-1.5 text-primary"
                      >
                        <Video className="h-4 w-4" /> Back to Video
                      </Button>
                    </div>

                    <div className="p-4 bg-card rounded-md border mb-4">
                      <p className="mb-2 font-medium">
                        {currentKeyMoment?.challenge}
                      </p>
                    </div>

                    <CourseEditor
                      code={code}
                      onChange={setCode}
                      language="javascript"
                      hints={currentKeyMoment?.hints || []}
                      onSkip={handleSkipChallenge}
                      onSubmit={handleSubmitCode}
                      onReset={handleResetCode}
                      initialCode={
                        currentKeyMoment?.id === "1"
                          ? "// Write your solution here\nfunction isEven(num) {\n  // Your code here\n}"
                          : currentKeyMoment?.id === "2"
                          ? "// Write your solution here\nfunction capitalize(str) {\n  // Your code here\n}"
                          : "// Write your solution here\nfunction filterPositive(numbers) {\n  // Your code here\n}"
                      }
                    />

                    {showFeedback && (
                      <div className="mt-4">
                        <CourseFeedback
                          code={code}
                          expectedSolution={
                            currentKeyMoment?.id === "1"
                              ? "return num % 2 === 0"
                              : currentKeyMoment?.id === "2"
                              ? "return str.charAt(0).toUpperCase() + str.slice(1)"
                              : "return numbers.filter(num => num >= 0)"
                          }
                          onClose={handleContinue}
                          onSkip={handleSkipChallenge}
                        />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="content-wrapper">
                    {steps[currentStep - 1].content && (
                      <div className="bg-muted/50 rounded-lg p-6 border">
                        <h3 className="font-semibold text-lg mb-2">
                          Understanding JavaScript Fundamentals
                        </h3>
                        <p className="text-muted-foreground mb-4">
                          In this lesson, we'll learn essential JavaScript
                          functions and methods for everyday coding tasks.
                        </p>
                        <CourseVideoPlayer
                          videoUrl={sampleVideoUrl}
                          keyMoments={keyMoments}
                          onKeyMomentEncountered={handleKeyMomentEncountered}
                          onComplete={handleVideoComplete}
                          showVideo={!showEditor}
                          resumeVideo={resumeVideo}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="how" className="mt-6">
            <div className="bg-card rounded-lg border overflow-hidden">
              <div className="bg-muted px-6 py-4 border-b">
                <h3 className="font-semibold text-lg">
                  How Our Interactive Learning Works
                </h3>
              </div>
              <div className="p-6">
                <div className="grid gap-10">
                  {steps.map((step, index) => (
                    <div key={index} className="flex gap-6 items-start">
                      <div className="bg-primary/10 rounded-full p-4 flex-shrink-0">
                        {step.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-2 flex items-center gap-2">
                          <span className="bg-primary/20 text-primary rounded-full w-6 h-6 flex items-center justify-center text-sm">
                            {index + 1}
                          </span>
                          {step.title}
                        </h3>
                        <p className="text-muted-foreground mb-4">
                          {step.description}
                        </p>

                        {index === 0 && (
                          <div className="bg-muted/50 p-4 rounded-md">
                            <p className="text-sm">
                              Our video lessons feature expert instructors who
                              break down complex concepts into digestible
                              chunks. All videos include captions, timestamps,
                              and the ability to adjust playback speed.
                            </p>
                          </div>
                        )}

                        {index === 1 && (
                          <div className="bg-muted/50 p-4 rounded-md">
                            <p className="text-sm">
                              We've identified the optimal moments to introduce
                              hands-on practice. Instead of watching an entire
                              lecture and forgetting the details, you'll apply
                              what you've learned immediately.
                            </p>
                          </div>
                        )}

                        {index === 2 && (
                          <div className="bg-muted/50 p-4 rounded-md">
                            <p className="text-sm">
                              Our browser-based code editor supports syntax
                              highlighting, auto-completion, and real-time error
                              checking—no setup or installation required.
                            </p>
                          </div>
                        )}

                        {index === 3 && (
                          <div className="bg-muted/50 p-4 rounded-md">
                            <p className="text-sm">
                              Our AI feedback system doesn't just tell you if
                              your answer is right or wrong—it helps you
                              understand why and provides personalized guidance
                              to improve your code.
                            </p>
                          </div>
                        )}

                        {index === 4 && (
                          <div className="bg-muted/50 p-4 rounded-md">
                            <p className="text-sm">
                              This learning loop ensures you're not just
                              passively consuming content but actively engaging
                              with the material until you master it.
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-10 bg-primary/5 border border-primary/20 rounded-lg p-6">
                  <div className="flex items-start gap-4">
                    <Lightbulb className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
                    <div>
                      <h4 className="font-semibold mb-2">
                        Why This Method Works
                      </h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        Research shows that active learning with immediate
                        application and feedback leads to:
                      </p>
                      <ul className="grid sm:grid-cols-3 gap-4 text-sm">
                        <li className="bg-background p-3 rounded border">
                          <span className="font-medium block mb-1">
                            2-3x Faster Learning
                          </span>
                          Compared to traditional video tutorials
                        </li>
                        <li className="bg-background p-3 rounded border">
                          <span className="font-medium block mb-1">
                            80% Better Retention
                          </span>
                          Knowledge that sticks with you long-term
                        </li>
                        <li className="bg-background p-3 rounded border">
                          <span className="font-medium block mb-1">
                            Higher Engagement
                          </span>
                          Less procrastination, more completion
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <ChallengeModal />
    </div>
  );
};

export default TryDemo;
