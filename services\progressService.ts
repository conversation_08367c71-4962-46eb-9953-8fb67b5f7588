
interface CourseProgress {
  progress: number;
  lastAccessed: string;
  completedSections: string[];
  startDate: string;
  moduleProgress?: {
    [moduleId: string]: {
      progress: number;
      completedSections: string[];
    }
  };
}

interface UserProgressData {
  userId: string;
  courses: {
    [courseId: string]: CourseProgress;
  };
}

const progressService = {
  // Get user's progress data
  getUserProgress: (userId: string): UserProgressData | null => {
    const progressData = localStorage.getItem(`upskilleo-progress-${userId}`);
    return progressData ? JSON.parse(progressData) : null;
  },
  
  // Save user's progress data
  saveUserProgress: (progressData: UserProgressData): void => {
    localStorage.setItem(`upskilleo-progress-${progressData.userId}`, JSON.stringify(progressData));
  },
  
  // Initialize progress for a new course
  initializeCourseProgress: (userId: string, courseId: string): void => {
    const progressData = progressService.getUserProgress(userId) || {
      userId,
      courses: {}
    };
    
    if (!progressData.courses[courseId]) {
      progressData.courses[courseId] = {
        progress: 0,
        lastAccessed: new Date().toISOString(),
        completedSections: [],
        startDate: new Date().toISOString(),
        moduleProgress: {}
      };
      
      progressService.saveUserProgress(progressData);
    }
  },
  
  // Update section completion status
  updateSectionCompletion: (
    userId: string, 
    courseId: string, 
    moduleId: string, 
    sectionId: string, 
    completed: boolean
  ): UserProgressData | void => {
    const progressData = progressService.getUserProgress(userId);
    
    if (!progressData) return;
    
    // Initialize course progress if it doesn't exist
    if (!progressData.courses[courseId]) {
      progressService.initializeCourseProgress(userId, courseId);
      return progressService.updateSectionCompletion(userId, courseId, moduleId, sectionId, completed);
    }
    
    // Initialize module progress if needed
    if (!progressData.courses[courseId].moduleProgress) {
      progressData.courses[courseId].moduleProgress = {};
    }
    
    // Initialize specific module progress if needed
    if (!progressData.courses[courseId].moduleProgress![moduleId]) {
      progressData.courses[courseId].moduleProgress![moduleId] = {
        progress: 0,
        completedSections: []
      };
    }
    
    const sectionIdentifier = `${moduleId}:${sectionId}`;
    const moduleProgress = progressData.courses[courseId].moduleProgress![moduleId];
    const courseProgress = progressData.courses[courseId];
    
    if (completed) {
      // Add to completed sections if not already there
      if (!moduleProgress.completedSections.includes(sectionId)) {
        moduleProgress.completedSections.push(sectionId);
      }
      
      if (!courseProgress.completedSections.includes(sectionIdentifier)) {
        courseProgress.completedSections.push(sectionIdentifier);
      }
    } else {
      // Remove from completed sections
      moduleProgress.completedSections = moduleProgress.completedSections.filter(id => id !== sectionId);
      courseProgress.completedSections = courseProgress.completedSections.filter(id => id !== sectionIdentifier);
    }
    
    // Update last accessed timestamp
    courseProgress.lastAccessed = new Date().toISOString();
    
    progressService.saveUserProgress(progressData);
    
    // Return updated progress data
    return progressData;
  },
  
  // Calculate module progress
  calculateModuleProgress: (
    userId: string,
    courseId: string,
    moduleId: string,
    totalSections: number
  ): number => {
    const progressData = progressService.getUserProgress(userId);
    
    if (!progressData || 
        !progressData.courses[courseId] || 
        !progressData.courses[courseId].moduleProgress ||
        !progressData.courses[courseId].moduleProgress![moduleId]) {
      return 0;
    }
    
    const moduleProgress = progressData.courses[courseId].moduleProgress![moduleId];
    const completedCount = moduleProgress.completedSections.length;
    
    if (totalSections === 0) return 0;
    return Math.round((completedCount / totalSections) * 100);
  },
  
  // Calculate overall course progress
  calculateCourseProgress: (
    userId: string,
    courseId: string,
    totalSections: number
  ): number => {
    const progressData = progressService.getUserProgress(userId);
    
    if (!progressData || !progressData.courses[courseId]) {
      return 0;
    }
    
    const courseProgress = progressData.courses[courseId];
    const completedCount = courseProgress.completedSections.length;
    
    if (totalSections === 0) return 0;
    return Math.round((completedCount / totalSections) * 100);
  },
  
  // Get completed sections for a course
  getCompletedSections: (
    userId: string,
    courseId: string
  ): string[] => {
    const progressData = progressService.getUserProgress(userId);
    
    if (!progressData || !progressData.courses[courseId]) {
      return [];
    }
    
    return progressData.courses[courseId].completedSections;
  },
  
  // Check if a specific section is completed
  isSectionCompleted: (
    userId: string,
    courseId: string,
    moduleId: string,
    sectionId: string
  ): boolean => {
    const progressData = progressService.getUserProgress(userId);
    
    if (!progressData || 
        !progressData.courses[courseId] || 
        !progressData.courses[courseId].moduleProgress ||
        !progressData.courses[courseId].moduleProgress![moduleId]) {
      return false;
    }
    
    return progressData.courses[courseId].moduleProgress![moduleId].completedSections.includes(sectionId);
  }
};

export default progressService;
