const courses = [
  {
    id: "frontend",
    slug: "frontend",
    title: "Frontend Development Masterclass",
    description: "Master HTML, CSS, JavaScript, and React to build engaging user interfaces",
    longDescription: "This comprehensive course takes you from the fundamentals of web design to advanced frontend frameworks. You'll learn how to create responsive, accessible, and dynamic web applications using modern best practices and tools.",
    badge: "Most Popular",
    totalLessons: 35,
    level: "Beginner to Advanced",
    projects: 8,
    image: "https://images.unsplash.com/photo-1593720213428-28a5b9e94613?auto=format&fit=crop&q=80&w=1200",
    color: "from-blue-500 to-indigo-500",
    price: 79.99,
    instructor: "<PERSON>",
    rating: 4.8,
    reviews: 1245,
    students: 5430,
    lastUpdated: "October 2023",
    features: [
      "Lifetime access to 120+ lessons",
      "Hands-on projects with real-world applications",
      "Downloadable source code and resources",
      "Certificate of completion",
      "24/7 community support",
    ],
    topics: [
      "HTML5 semantics and best practices",
      "CSS3 layouts and animations",
      "Modern JavaScript (ES6+)",
      "React component architecture",
      "State management with Redux",
      "Responsive design principles",
      "Web accessibility (WCAG)",
      "Performance optimization",
    ],
    resources: [
      { link: "https://example.com/frontend-guide.pdf", filename: "Frontend Guide.pdf" },
    ],
    modules: [
      {
        id: "html-css",
        title: "HTML & CSS Fundamentals",
        description: "Learn the building blocks of the web",
        duration: "4 hours",
        lessons: 8,
        completed: false,
        locked: false,
        progress: 0,
        badge: "Beginner",
        track: "frontend",
        topics: [
          "HTML5 semantics and best practices",
          "CSS3 layouts and animations",
          "Modern JavaScript (ES6+)",
          "React component architecture",
          "State management with Redux",
          "Responsive design principles",
          "Web accessibility (WCAG)",
          "Performance optimization",
        ],
        sections: [
          {
            id: "html-intro",
            title: "Introduction to HTML",
            duration: "30 min",
            completed: false,
            locked: true,
            description: "Get started with the basics of HTML and its structure.",
            lessons: [
              {
                id: "html1",
                title: "HTML Introduction",
                description: "HTML Introduction",
                completed: false,
                locked: false,
                duration: "30",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "html1-bp1",
                    openAtDuration: 20,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: "" }
                    ]
                  },
                  {
                    id: "html1-bp2",
                    openAtDuration: 60,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: `console.log("hello")` }
                    ]
                  }
                ],
              },
              {
                id: "html1-placeholder-1",
                title: "Placeholder Lesson",
                description: "",
                duration: "10",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "html1-bp1",
                    openAtDuration: 4,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n <p>hello default</p> \n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: "" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "html1-placeholder-2",
                title: "Placeholder Lesson",
                description: "",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "html1-bp1",
                    openAtDuration: 6,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: "" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              }
            ]
          },
          {
            id: "css-basics",
            title: "CSS Styling Basics",
            duration: "45 min",
            completed: false,
            locked: true,
            description: "Learn how to style web pages using CSS.",
            lessons: [
              {
                id: "css1",
                title: "CSS Basics",
                description: "CSS Basics",
                duration: "30",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "css1-bp1",
                    openAtDuration: 100,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: "" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "css1-placeholder-1",
                title: "Placeholder Lesson",
                description: "",
                duration: "10",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "css1-bp1",
                    openAtDuration: 100,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: "" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "css1-placeholder-2",
                title: "Placeholder Lesson",
                description: "",
                duration: "10",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "css1-bp1",
                    openAtDuration: 100,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: "" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              }
            ]
          },
          {
            id: "responsive",
            title: "Responsive Design Principles",
            duration: "60 min",
            completed: false,
            locked: false,
            description: "Understand how to make layouts that work on any device.",
            lessons: [
              {
                id: "css2",
                title: "Flexbox & Grid",
                description: "Flexbox & Grid",
                duration: "60",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "css2-bp1",
                    openAtDuration: 100,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: "" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "css2-placeholder-1",
                title: "Placeholder Lesson",
                description: "",
                duration: "10",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "css2-bp1",
                    openAtDuration: 100,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: "" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "css2-placeholder-2",
                title: "Placeholder Lesson",
                description: "",
                duration: "10",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "css2-bp1",
                    openAtDuration: 100,
                    challengeType: "code",
                    editorSettings: [
                      {
                        type: "html",
                        boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                      },
                      { type: "css", boilerplateCode: "" },
                      { type: "js", boilerplateCode: "" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              }
            ]
          }
        ],
        totalHours: 4,
        totalLessons: 8,
        labels: [{ title: "Essentials", type: "p1" }],
      },
      {
        id: "javascript",
        title: "JavaScript Essentials",
        description: "Master the core language of web development",
        duration: "6 hours",
        lessons: 12,
        completed: false,
        locked: false,
        progress: 0,
        track: "frontend",
        topics: [
          "JavaScript syntax and data types",
          "Functions and scope",
          "DOM manipulation",
          "Event handling",
          "Asynchronous JavaScript",
        ],
        sections: [

          {
            id: "js-arrays",
            title: "JavaScript Arrays",
            duration: "50 min",
            completed: false,
            locked: false,
            lessons: [
              {
                id: "js-arrays-1",
                title: "Array Basics",
                description: "Learn about array creation and basic operations.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-arrays-1",
                    openAtDuration: 60,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Write an array example" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "js-arrays-2",
                title: "Array Methods",
                description: "Explore common array methods like map, filter, reduce.",
                duration: "20",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-arrays-2",
                    openAtDuration: 90,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Use map/filter/reduce" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "js-arrays-3",
                title: "Multidimensional Arrays",
                description: "Work with arrays of arrays.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-arrays-3",
                    openAtDuration: 120,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Create a 2D array" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              }
            ]
          },
          {
            id: "js-objects",
            title: "JavaScript Objects",
            duration: "50 min",
            completed: false,
            locked: false,
            lessons: [
              {
                id: "js-objects-1",
                title: "Object Basics",
                description: "Learn about object creation and properties.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-objects-1",
                    openAtDuration: 60,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Create an object" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "js-objects-2",
                title: "Object Methods",
                description: "Define and use methods on objects.",
                duration: "20",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-objects-2",
                    openAtDuration: 90,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Add a method to an object" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              },
              {
                id: "js-objects-3",
                title: "Object Inheritance",
                description: "Understand prototype-based inheritance.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-objects-3",
                    openAtDuration: 120,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Demonstrate inheritance" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              }
            ]
          }
        ],
        totalHours: 6,
        totalLessons: 12,
        labels: [{ title: "Core JS", type: "p1" }],
        lessons: [
          {
            id: "js1",
            title: "Variables and Types",
            description: "Variables and Types",
            duration: "30",
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
            breakpoints: [
              {
                openAtDuration: 100,
                challengeType: "code",
                editorSettings: [
                  {
                    type: "html",
                    boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                  },
                  {
                    type: "css",
                    boilerplateCode: ""
                  },
                  {
                    type: "js",
                    boilerplateCode: ""
                  }
                ]
              }
            ],
            completed: false,
            locked: false,
          },
          {
            id: "js2",
            title: "Functions & Scope",
            description: "Functions & Scope",
            duration: "45",
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
            breakpoints: [
              {
                openAtDuration: 100,
                challengeType: "code",
                editorSettings: [
                  {
                    type: "html",
                    boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                  },
                  {
                    type: "css",
                    boilerplateCode: ""
                  },
                  {
                    type: "js",
                    boilerplateCode: ""
                  }
                ]
              }
            ],
            completed: false,
            locked: true,
          },
          {
            id: "js3",
            title: "DOM Manipulation",
            description: "DOM Manipulation",
            duration: "45",
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
            breakpoints: [
              {
                openAtDuration: 100,
                challengeType: "code",
                editorSettings: [
                  {
                    type: "html",
                    boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                  },
                  {
                    type: "css",
                    boilerplateCode: ""
                  },
                  {
                    type: "js",
                    boilerplateCode: ""
                  }
                ]
              }
            ],
            completed: false,
            locked: true,
          },
          {
            id: "js4",
            title: "Events",
            description: "Events",
            duration: "60",
            videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
            breakpoints: [
              {
                openAtDuration: 100,
                challengeType: "code",
                editorSettings: [
                  {
                    type: "html",
                    boilerplateCode: "<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n</body>\n</html>"
                  },
                  {
                    type: "css",
                    boilerplateCode: ""
                  },
                  {
                    type: "js",
                    boilerplateCode: ""
                  }
                ]
              }
            ],
            completed: false,
            locked: true,
          }
        ]
      },
      {
        id: "react",
        title: "React Front-End Development",
        description: "Build dynamic user interfaces with React",
        duration: "8 hours",
        lessons: 15,
        completed: false,
        locked: true,
        progress: 0,
        track: "frontend",
        topics: [
          "React fundamentals and core concepts",
          "Component lifecycle and hooks",
          "State management with useState and useReducer",
          "Context API and prop drilling",
          "Custom hooks and reusable logic",
          "React Router for navigation",
          "Form handling and validation",
          "Performance optimization techniques",
        ],
        sections: [
          {
            id: "react-basics",
            title: "React Basics",
            duration: "50 min",
            completed: false,
            locked: false,
            lessons: [
              {
                id: "react-basics-1",
                title: "JSX and Rendering",
                description: "Learn about JSX and rendering elements in React.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-react-basics-1",
                    openAtDuration: 60,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Render a React element" }
                    ]
                  }
                ]
              },
              {
                id: "react-basics-2",
                title: "Components and Props",
                description: "Understand functional components and props.",
                duration: "20",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-react-basics-2",
                    openAtDuration: 90,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Create a component with props" }
                    ]
                  }
                ]
              },
              {
                id: "react-basics-3",
                title: "State and Events",
                description: "Manage state and handle events in React.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-react-basics-3",
                    openAtDuration: 120,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Use useState and handle an event" }
                    ]
                  }
                ]
              }
            ]
          },
          {
            id: "react-hooks",
            title: "React Hooks",
            duration: "50 min",
            completed: false,
            locked: false,
            lessons: [
              {
                id: "react-hooks-1",
                title: "useEffect Hook",
                description: "Learn about the useEffect hook for side effects.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-react-hooks-1",
                    openAtDuration: 60,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Use useEffect for data fetching" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "react-hooks-2",
                title: "Custom Hooks",
                description: "Create and use custom hooks.",
                duration: "20",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-react-hooks-2",
                    openAtDuration: 90,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Create a custom hook" }
                    ]
                  }
                ],
                completed: false,
                locked: false,
              },
              {
                id: "react-hooks-3",
                title: "useContext Hook",
                description: "Share state using the useContext hook.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-react-hooks-3",
                    openAtDuration: 120,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Use useContext for global state" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              }
            ]
          },
          {
            id: "react-routing",
            title: "React Routing",
            duration: "50 min",
            completed: false,
            locked: false,
            lessons: [
              {
                id: "react-routing-1",
                title: "React Router Basics",
                description: "Set up routing in a React app.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-react-routing-1",
                    openAtDuration: 60,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Set up a basic route" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              },
              {
                id: "react-routing-2",
                title: "Dynamic Routing",
                description: "Implement dynamic routes in React Router.",
                duration: "20",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-react-routing-2",
                    openAtDuration: 90,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Create a dynamic route" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              },
              {
                id: "react-routing-3",
                title: "Route Guards",
                description: "Protect routes with guards in React Router.",
                duration: "15",
                videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                breakpoints: [
                  {
                    id: "bp-react-routing-3",
                    openAtDuration: 120,
                    challengeType: "code",
                    editorSettings: [
                      { type: "js", boilerplateCode: "// Implement a route guard" }
                    ]
                  }
                ],
                completed: false,
                locked: true,
              }
            ]
          }
        ],
        totalHours: 8,
        totalLessons: 15,
        labels: [{ title: "React", type: "p2" }],
        lessons: [
          { id: "react1", title: "Components & Props", duration: "30" },
          { id: "react2", title: "State Management", duration: "60" },
          { id: "react3", title: "Hooks", duration: "60" },
          { id: "react4", title: "React Router", duration: "30" },
        ],
      },
    ],
  },
  {
    id: "backend",
    slug: "backend",
    title: "Backend Development with Node.js",
    description: "Learn server-side programming with Node.js, Express, and MongoDB",
    longDescription: "Become a backend expert with this hands-on course that covers everything from server basics to advanced API development and database integration. Perfect for developers looking to expand their skills to the server side.",
    badge: "High Demand",
    totalLessons: 36,
    level: "Intermediate",
    projects: 6,
    image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?auto=format&fit=crop&q=80&w=1200",
    color: "from-green-500 to-emerald-500",
    price: 89.99,
    instructor: "Michael Chen",
    rating: 4.7,
    reviews: 893,
    students: 3120,
    lastUpdated: "November 2023",
    features: [
      "Lifetime access to 90+ lessons",
      "5 complete backend projects",
      "API documentation and testing techniques",
      "Database design practices",
      "Deployment strategies",
    ],
    topics: [
      "Node.js environment and modules",
      "Express.js framework",
      "RESTful API development",
      "MongoDB and Mongoose ORM",
      "JWT authentication",
      "Error handling and validation",
      "Deployment to cloud platforms",
      "Testing with Jest and Supertest",
    ],
    resources: [
      { link: "https://example.com/backend-notes.pdf", filename: "Backend Notes.pdf" },
    ],
    modules: [
      {
        id: "node-basics",
        title: "Node.js Fundamentals",
        description: "Learn the basics of server-side JavaScript",
        duration: "5 hours",
        lessons: 10,
        completed: false,
        locked: false,
        progress: 0,
        badge: "Beginner",
        track: "backend",
        sections: [
          {
            id: "node-intro",
            title: "Introduction to Node.js",
            duration: "45 min",
            completed: false,
            locked: false,
          },
          {
            id: "modules",
            title: "Node.js Modules System",
            duration: "60 min",
            completed: false,
            locked: true,
          },
        ],
        totalHours: 5,
        totalLessons: 10,
        labels: [{ title: "Node", type: "p1" }],
        lessons: [
          { id: "node1", title: "Intro to Node.js", duration: "30" },
          { id: "node2", title: "Modules & NPM", duration: "30" },
          { id: "node3", title: "Async JS", duration: "60" },
        ],
      },
      {
        id: "express",
        title: "Express.js Framework",
        description: "Build robust web applications with Express",
        duration: "6 hours",
        lessons: 12,
        completed: false,
        locked: false,
        progress: 0,
        track: "backend",
        sections: [
          {
            id: "express-intro",
            title: "Getting Started with Express",
            duration: "45 min",
            completed: false,
            locked: false,
          },
          {
            id: "middleware",
            title: "Express Middleware",
            duration: "60 min",
            completed: false,
            locked: true,
          },
        ],
        totalHours: 6,
        totalLessons: 12,
        labels: [{ title: "APIs", type: "p1" }],
        lessons: [
          { id: "exp1", title: "Routing", duration: "45" },
          { id: "exp2", title: "Middleware", duration: "45" },
          { id: "exp3", title: "Error Handling", duration: "45" },
        ],
      },
      {
        id: "mongodb",
        title: "MongoDB Database Integration",
        description: "Connect your application to MongoDB",
        duration: "7 hours",
        lessons: 14,
        completed: false,
        locked: true,
        progress: 0,
        track: "backend",
        sections: [
          {
            id: "mongo-intro",
            title: "MongoDB Basics",
            duration: "60 min",
            completed: false,
            locked: true,
          },
          {
            id: "mongoose",
            title: "Mongoose ODM",
            duration: "75 min",
            completed: false,
            locked: true,
          },
        ],
        totalHours: 7,
        totalLessons: 14,
        labels: [{ title: "Database", type: "p2" }],
        lessons: [
          { id: "db1", title: "MongoDB Basics", duration: "45" },
          { id: "db2", title: "Mongoose ODM", duration: "45" },
          { id: "deploy1", title: "Heroku Deployment", duration: "45" },
        ],
      },
    ],
  },
  {
    id: "fullstack",
    slug: "fullstack",
    title: "Full-Stack JavaScript Development",
    description: "Comprehensive path covering both frontend and backend technologies for complete application development",
    longDescription: "This complete course bridges the gap between frontend and backend development, enabling you to build full-stack applications from scratch. You'll learn to integrate React frontends with Node.js backends and deploy complete applications.",
    badge: "Comprehensive",
    totalLessons: 60,
    level: "Beginner to Expert",
    projects: 12,
    image: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?auto=format&fit=crop&q=80&w=1200",
    color: "from-purple-500 to-pink-500",
    price: 129.99,
    instructor: "Alex Rivera",
    rating: 4.9,
    reviews: 1578,
    students: 7850,
    lastUpdated: "December 2023",
    features: [
      "Lifetime access to 180+ lessons",
      "3 complete full-stack projects",
      "Front-to-back integration techniques",
      "Authentication and authorization",
      "CI/CD and deployment workflows",
    ],
    topics: [
      "JavaScript fundamentals (ES6+)",
      "React frontend development",
      "Node.js and Express backend",
      "MongoDB database integration",
      "RESTful and GraphQL APIs",
      "Authentication with JWT",
      "State management",
      "Deployment to Vercel and Heroku",
    ],
    resources: [
      { link: "https://example.com/fullstack-roadmap.pdf", filename: "Fullstack Roadmap.pdf" },
    ],
    modules: [
      {
        id: "js-foundations",
        title: "JavaScript Foundations",
        description: "Master modern JavaScript for both frontend and backend",
        duration: "8 hours",
        lessons: 15,
        completed: false,
        locked: false,
        progress: 0,
        badge: "Beginner",
        track: "fullstack",
        sections: [
          {
            id: "es6-basics",
            title: "ES6+ Fundamentals",
            duration: "90 min",
            completed: false,
            locked: false,
          },
          {
            id: "async-js",
            title: "Asynchronous JavaScript",
            duration: "75 min",
            completed: false,
            locked: true,
          },
        ],
        totalHours: 8,
        totalLessons: 15,
        labels: [{ title: "Review", type: "p0" }],
        lessons: [
          { id: "fsf1", title: "HTML/CSS Recap", duration: "45" },
          { id: "fsf2", title: "React Overview", duration: "45" },
        ],
      },
      {
        id: "react-frontend",
        title: "React Frontend Development",
        description: "Build dynamic user interfaces with React",
        duration: "10 hours",
        lessons: 20,
        completed: false,
        locked: false,
        progress: 0,
        track: "fullstack",
        sections: [
          {
            id: "react-core",
            title: "React Core Concepts",
            duration: "60 min",
            completed: false,
            locked: false,
          },
          {
            id: "hooks",
            title: "React Hooks",
            duration: "75 min",
            completed: false,
            locked: true,
          },
        ],
        totalHours: 10,
        totalLessons: 20,
        labels: [{ title: "Review", type: "p0" }],
        lessons: [
          { id: "fsb1", title: "Node.js Recap", duration: "45" },
          { id: "fsb2", title: "API Basics", duration: "45" },
        ],
      },
      {
        id: "node-backend",
        title: "Node.js Backend Development",
        description: "Create robust server-side applications",
        duration: "12 hours",
        lessons: 25,
        completed: false,
        locked: true,
        progress: 0,
        track: "fullstack",
        sections: [
          {
            id: "express-apis",
            title: "Building RESTful APIs",
            duration: "90 min",
            completed: false,
            locked: true,
          },
          {
            id: "mongodb-integration",
            title: "MongoDB Integration",
            duration: "75 min",
            completed: false,
            locked: true,
          },
        ],
        totalHours: 12,
        totalLessons: 25,
        labels: [{ title: "Project", type: "p2" }],
        lessons: [
          { id: "fsp1", title: "Setup & Boilerplate", duration: "30" },
          { id: "fsp2", title: "Auth System", duration: "60" },
          { id: "fsp3", title: "Dashboard UI", duration: "60" },
          { id: "fsp4", title: "CRUD Operations", duration: "60" },
          { id: "fsp5", title: "API Integration", duration: "60" },
          { id: "fsp6", title: "Deploy Full App", duration: "60" },
        ],
      },
    ],
  },
];

module.exports = { courses };
