'use client';

import React, { useState, useEffect } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import LearningDashboard from '@/components/LearningDashboard';
import { Button } from "@/components/ui/button";
import { BookOpen } from 'lucide-react';
import userService from '@/services/userService';

const Learning = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check authentication using user service
    const hasValidAuth = userService.hasValidAuth();
    const userData = userService.getUserData();
    
    if (hasValidAuth && userData) {
      setIsLoggedIn(true);
    } else {
      // Clear invalid auth data
      userService.clearUserData();
      setIsLoggedIn(false);
    }
    
    setIsLoading(false);
  }, []);

  const handleLogin = () => {
    // Redirect to auth page with current page as redirect
    window.location.href = `/auth?redirect=${encodeURIComponent('/learning')}`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-b from-background to-background/95">
        <Navbar />
        <main className="flex-grow flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-background to-background/95">
      <Navbar />
      <main className="flex-grow relative overflow-hidden">
        {/* Enhanced Background Elements with Parallax Effect */}
        <div className="fixed inset-0 z-0 pointer-events-none">
          <div className="absolute -top-20 -right-40 w-[40rem] h-[40rem] bg-upskilleo-purple/10 rounded-full blur-3xl transform-gpu"></div>
          <div className="absolute top-1/3 -left-60 w-[35rem] h-[35rem] bg-blue-500/10 rounded-full blur-3xl transform-gpu"></div>
          <div className="absolute bottom-0 right-0 w-[30rem] h-[30rem] bg-secondary/10 rounded-full blur-3xl transform-gpu"></div>
          <div className="absolute top-20 left-1/4 w-32 h-32 border border-primary/10 rounded-full opacity-20 animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-24 h-24 border border-primary/20 rounded-full opacity-30 animate-pulse"></div>
          
          {/* Code pattern overlay for tech vibe */}
          <div className="absolute inset-0 opacity-5 bg-[url('https://images.unsplash.com/photo-1542831371-29b0f74f9713?q=80&w=2000&auto=format&fit=crop')] bg-repeat bg-center mix-blend-overlay"></div>
        </div>
        
        <div className="py-12 sm:py-20 px-4 sm:px-6 relative z-10">
          {isLoggedIn ? (
            <LearningDashboard />
          ) : (
            <div className="container mx-auto max-w-6xl">
              {/* Enhanced Header with visual flair */}
              <div className="text-center mb-16 animate-fade-in">
                <div className="inline-block mb-3">
                  <div className="relative">
                    <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-primary/30 via-purple-500/30 to-secondary/30 blur-xl opacity-70"></div>
                    <span className="relative bg-background/80 p-3 rounded-full inline-flex items-center justify-center">
                      <BookOpen className="h-8 w-8 text-primary" />
                    </span>
                  </div>
                </div>
                <h2 className="text-3xl sm:text-5xl font-bold mb-4 tracking-tight">
                  Your <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-500">Learning</span> Dashboard
                </h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                  Track your progress, continue learning, and manage your enrolled courses in one place.
                </p>
              </div>
              
              <div className="max-w-md mx-auto bg-card/95 backdrop-blur-sm rounded-xl border border-border shadow-lg overflow-hidden">
                <div className="relative h-40 overflow-hidden">
                  <img 
                    src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=2070&auto=format&fit=crop"
                    alt="Learning Dashboard" 
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-card via-transparent to-transparent"></div>
                </div>
                <div className="p-8 text-center">
                  <h3 className="text-2xl font-semibold mb-3">Sign in to access your learning</h3>
                  <p className="text-muted-foreground mb-8">
                    Track your progress, access your courses, and continue your learning journey with personalized recommendations.
                  </p>
                  <div className="flex gap-4 justify-center">
                    <Button 
                      className="px-6 py-5 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                      onClick={handleLogin}
                    >
                      Sign In
                    </Button>
                    <Button variant="outline" className="px-6 py-5">
                      Sign Up
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Learning;
