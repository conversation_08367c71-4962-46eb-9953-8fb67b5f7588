
import React, { useEffect, useState, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Terminal, Maximize2, Minimize2, Chrome, XCircle, RefreshCw, ArrowDown } from 'lucide-react';

interface PreviewIframeProps {
  htmlCode: string;
  cssCode: string;
  jsCode: string;
  language?: string;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
  forceRefresh?: boolean;
}

const PreviewIframe: React.FC<PreviewIframeProps> = ({ 
  htmlCode, 
  cssCode, 
  jsCode,
  language,
  isFullscreen,
  onToggleFullscreen,
  forceRefresh = false
}) => {
  const [iframeContent, setIframeContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [showConsole, setShowConsole] = useState(true);
  const [consoleLogs, setConsoleLogs] = useState<{type: string, content: string}[]>([]);
  const [refreshKey, setRefreshKey] = useState<number>(0); // Used to force refresh
  const prevForceRefreshRef = useRef<boolean>(forceRefresh);
  const prevJsCodeRef = useRef<string>(jsCode);
  const prevLanguageRef = useRef<string | undefined>(language);
  const consoleRef = useRef<HTMLDivElement>(null);
  
  // Clear console logs
  const clearConsoleLogs = () => {
    setConsoleLogs([]);
  };
  
  // Force refresh when forceRefresh prop changes or when code/language changes
  useEffect(() => {
    if (
      forceRefresh !== prevForceRefreshRef.current || 
      jsCode !== prevJsCodeRef.current ||
      language !== prevLanguageRef.current
    ) {
      setRefreshKey(prev => prev + 1);
      prevForceRefreshRef.current = forceRefresh;
      prevJsCodeRef.current = jsCode;
      prevLanguageRef.current = language;
      
      // Clear console logs when refreshing
      clearConsoleLogs();
    }
  }, [forceRefresh, jsCode, language]);
  
  // Scroll to the bottom of console
  const scrollToBottom = () => {
    if (consoleRef.current) {
      consoleRef.current.scrollTo({
        top: consoleRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  useEffect(() => {
    // Clear console logs when code changes
    clearConsoleLogs();
    
    // For regular HTML/CSS/JS
    const content = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Preview</title>
          <style>${cssCode}</style>
        </head>
        <body>
          ${htmlCode}
          <script>
            // Flag to track if an error has been reported
            let errorReported = false;
            
            // Custom console to capture logs
            const originalConsole = {
              log: console.log,
              info: console.info,
              warn: console.warn,
              error: console.error
            };
            
            window.consoleMessages = [];
            
            console.log = function() {
              originalConsole.log.apply(console, arguments);
              const message = Array.from(arguments).map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
              ).join(' ');
              window.consoleMessages.push({type: 'log', content: message});
              window.parent.postMessage({type: 'console', msgType: 'log', content: message}, '*');
            };
            
            console.info = function() {
              originalConsole.info.apply(console, arguments);
              const message = Array.from(arguments).map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
              ).join(' ');
              window.consoleMessages.push({type: 'info', content: message});
              window.parent.postMessage({type: 'console', msgType: 'info', content: message}, '*');
            };
            
            console.warn = function() {
              originalConsole.warn.apply(console, arguments);
              const message = Array.from(arguments).map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
              ).join(' ');
              window.consoleMessages.push({type: 'warn', content: message});
              window.parent.postMessage({type: 'console', msgType: 'warn', content: message}, '*');
            };
            
            console.error = function() {
              originalConsole.error.apply(console, arguments);
              const message = Array.from(arguments).map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
              ).join(' ');
              window.consoleMessages.push({type: 'error', content: message});
              window.parent.postMessage({type: 'console', msgType: 'error', content: message}, '*');
            };
            
            // Set up window.onerror to catch all JavaScript errors
            window.onerror = function(message, source, lineno, colno, error) {
              if (!errorReported) {
                errorReported = true;
                const errorMsg = 'Error: ' + message + ' (line ' + lineno + ', col ' + colno + ')';
                console.error(errorMsg);
                // Reset error reported flag after a short delay
                setTimeout(() => { errorReported = false; }, 100);
                return true; // Prevents the error from showing in the browser's console
              }
              return true;
            };
            
            try {
              ${jsCode}
            } catch (error) {
              if (!errorReported) {
                errorReported = true;
                console.error('Error in preview:', error.message);
                // Prevent further error reporting for this error
                setTimeout(() => { errorReported = false; }, 100);
              }
            }
          </script>
        </body>
      </html>
    `;
    
    setIframeContent(content);
    setLoading(false);
  }, [htmlCode, cssCode, jsCode, language, refreshKey]);

  // Listen for console messages from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'console') {
        setConsoleLogs(prev => {
          // Check if this exact message is already in the console logs (prevent duplicates)
          const isDuplicate = prev.some(
            log => log.type === event.data.msgType && log.content === event.data.content
          );
          
          if (isDuplicate) {
            return prev;
          }
          
          return [...prev, {
            type: event.data.msgType,
            content: event.data.content
          }];
        });
      }
    };
    
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Manual refresh function
  const handleManualRefresh = () => {
    setRefreshKey(prev => prev + 1);
    clearConsoleLogs();
  };

  // Chrome-like browser interface wrapper for all content
  const ChromeBrowserFrame = ({children}: {children: React.ReactNode}) => (
    <div className="flex flex-col w-full h-full bg-white rounded-md overflow-hidden shadow border border-gray-200">
      {/* Chrome-like address bar */}
      <div className="bg-gray-100 border-b border-gray-200 flex items-center p-2">
        <div className="flex items-center space-x-1.5 mr-2">
          <div className="w-3 h-3 rounded-full bg-red-400"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
          <div className="w-3 h-3 rounded-full bg-green-400"></div>
        </div>
        <Chrome className="h-4 w-4 text-gray-500 mr-2" />
        <div className="bg-white flex-1 px-3 py-1 text-xs text-gray-500 rounded border border-gray-200 truncate">
          localhost:3000/preview
        </div>
        
        {/* Move action buttons to the address bar */}
        <div className="flex gap-2 ml-2">
          {/* Add refresh button */}
          <Button
            variant="outline"
            size="icon"
            onClick={handleManualRefresh}
            className="h-6 w-6 bg-transparent hover:bg-gray-200 border-none"
            title="Refresh Preview"
          >
            <RefreshCw className="h-3 w-3 text-gray-500" />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            onClick={() => setShowConsole(prev => !prev)}
            className="h-6 w-6 bg-transparent hover:bg-gray-200 border-none"
            title={showConsole ? "Hide Console" : "Show Console"}
          >
            <Terminal className="h-3 w-3 text-gray-500" />
          </Button>
          
          {onToggleFullscreen && (
            <Button 
              variant="outline"
              size="icon"
              onClick={onToggleFullscreen}
              className="h-6 w-6 bg-transparent hover:bg-gray-200 border-none"
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? <Minimize2 className="h-3 w-3 text-gray-500" /> : <Maximize2 className="h-3 w-3 text-gray-500" />}
            </Button>
          )}
        </div>
      </div>
      
      {/* Content area */}
      <div className="flex-1 relative overflow-hidden">
        {children}
      </div>
    </div>
  );

  return (
    <ChromeBrowserFrame>
      <div className={`${isFullscreen ? 'w-full h-full' : 'w-full h-[500px]'} bg-white relative flex flex-col`}>
        <div className={`flex-grow ${showConsole ? (isFullscreen ? 'h-[70vh]' : 'h-2/3') : 'h-full'}`}>
          {loading ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <p>Loading preview...</p>
            </div>
          ) : (
            iframeContent && (
              <iframe
                srcDoc={iframeContent}
                title="Preview"
                className="w-full h-full border-none"
                sandbox="allow-scripts allow-same-origin"
                key={`preview-${refreshKey}`} // Use refreshKey to force re-render
              />
            )
          )}
        </div>
        
        {/* Console Panel */}
        {showConsole && (
          <div 
            ref={consoleRef}
            className={`bg-[#1e1e1e] text-white ${isFullscreen ? 'max-h-[30vh]' : 'h-1/3'} overflow-y-auto font-mono text-sm p-2 border-t border-gray-700 relative`}
          >
            {/* Chrome-like console header */}
            <div className="flex justify-between items-center pb-2 border-b border-gray-700 mb-2">
              <div className="text-xs text-gray-400 font-sans">Console</div>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowConsole(false)}
                  className="h-5 w-5 p-0 hover:bg-gray-700 rounded-full"
                  title="Close Console"
                >
                  <XCircle className="h-3 w-3 text-gray-400" />
                </Button>
              </div>
            </div>
            
            {consoleLogs.length > 0 ? (
              consoleLogs.map((log, index) => (
                <div 
                  key={index} 
                  className={`py-1 border-b border-gray-700 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'warn' ? 'text-yellow-400' : 
                    log.type === 'info' ? 'text-blue-400' : 
                    'text-gray-300'
                  }`}
                >
                  <span className="opacity-70">{log.type}: </span>
                  {log.content}
                </div>
              ))
            ) : (
              <div className="text-gray-500 italic">No console output</div>
            )}
          </div>
        )}
        
        {/* Scroll to bottom button - only show in fullscreen mode */}
        {isFullscreen && (
          <Button
            variant="outline"
            size="icon"
            onClick={scrollToBottom}
            className="fixed bottom-6 right-6 h-10 w-10 rounded-full shadow-lg bg-primary hover:bg-primary/90 border-none z-50"
            title="Scroll to Bottom"
          >
            <ArrowDown className="h-5 w-5 text-white" />
          </Button>
        )}
      </div>
    </ChromeBrowserFrame>
  );
};

export default PreviewIframe;
