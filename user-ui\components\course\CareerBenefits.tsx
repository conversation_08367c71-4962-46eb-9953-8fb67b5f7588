
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Award, Briefcase, FileText, User, ChevronRight } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";

const CareerBenefits = () => {
  return (
    <section className="py-12 bg-gradient-to-br from-indigo-50/80 via-white to-purple-50/60">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold mb-4 text-gray-800">Career Enhancement Benefits</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Our courses are designed to give you more than just knowledge. Get career-boosting benefits that set you apart in the job market.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-blue-200 hover:shadow-md transition-all bg-white">
            <CardHeader className="pb-2">
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4 border border-blue-200">
                <Award className="h-6 w-6 text-blue-600" />
              </div>
              <CardTitle className="text-lg text-gray-800">Professional Certificate</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-700 mb-4 font-medium">
                Earn an industry-recognized certificate to showcase your skills and knowledge to potential employers.
              </p>
              <Button variant="ghost" size="sm" className="text-blue-600 p-0 h-auto hover:bg-blue-50">
                Learn more <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </CardContent>
          </Card>
          
          <Card className="border-amber-200 hover:shadow-md transition-all bg-white">
            <CardHeader className="pb-2">
              <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mb-4 border border-amber-200">
                <Briefcase className="h-6 w-6 text-amber-600" />
              </div>
              <CardTitle className="text-lg text-gray-800">Internship Opportunities</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-700 mb-4 font-medium">
                Top-performing students get exclusive access to internship opportunities with our industry partners.
              </p>
              <Button variant="ghost" size="sm" className="text-amber-600 p-0 h-auto hover:bg-amber-50">
                Learn more <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </CardContent>
          </Card>
          
          <Card className="border-green-200 hover:shadow-md transition-all bg-white">
            <CardHeader className="pb-2">
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4 border border-green-200">
                <User className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-lg text-gray-800">Career Placement</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-700 mb-4 font-medium">
                Get support from career coaches who will help you prepare for interviews and connect with hiring partners.
              </p>
              <Button variant="ghost" size="sm" className="text-green-600 p-0 h-auto hover:bg-green-50">
                Learn more <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </CardContent>
          </Card>
          
          <Card className="border-purple-200 hover:shadow-md transition-all bg-white">
            <CardHeader className="pb-2">
              <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4 border border-purple-200">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
              <CardTitle className="text-lg text-gray-800">UpskillEO Portfolio</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-700 mb-4 font-medium">
                Showcase your projects and achievements in a professional portfolio visible to our network of employers.
              </p>
              <Button variant="ghost" size="sm" className="text-purple-600 p-0 h-auto hover:bg-purple-50">
                Learn more <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default CareerBenefits;
