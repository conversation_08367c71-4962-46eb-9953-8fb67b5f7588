export interface UserData {
  id: string;
  name: string;
  email?: string;
  enrolledCoursesCount?: number;
  enrolledCourseIds?: string[];
}

class UserService {
  /**
   * Get current user data from localStorage
   */
  getUserData(): UserData | null {
    if (typeof window === "undefined") return null;
    
    try {
      const userData = localStorage.getItem("upskilleo-user");
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("Error parsing user data:", error);
      return null;
    }
  }

  /**
   * Check if user is logged in
   */
  isLoggedIn(): boolean {
    if (typeof window === "undefined") return false;
    const token = localStorage.getItem("token");
    const userData = localStorage.getItem("upskilleo-user");
    return !!(token && userData);
  }

  /**
   * Check if user has valid authentication (both token and user data)
   */
  hasValidAuth(): boolean {
    if (typeof window === "undefined") return false;
    const token = localStorage.getItem("token");
    const userData = localStorage.getItem("upskilleo-user");
    
    if (!token || !userData) return false;
    
    try {
      JSON.parse(userData); // Verify user data is valid JSON
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if user is enrolled in a specific course
   */
  isEnrolledInCourse(courseId: string): boolean {
    const userData = this.getUserData();
    if (!userData || !userData.enrolledCourseIds) return false;
    return userData.enrolledCourseIds.includes(courseId);
  }

  /**
   * Get all enrolled course IDs
   */
  getEnrolledCourseIds(): string[] {
    const userData = this.getUserData();
    return userData?.enrolledCourseIds || [];
  }

  /**
   * Get enrolled courses count
   */
  getEnrolledCoursesCount(): number {
    const userData = this.getUserData();
    return userData?.enrolledCoursesCount || 0;
  }

  /**
   * Update user enrollment data after enrolling in a course
   */
  updateEnrollmentData(courseId: string): void {
    if (typeof window === "undefined") return;
    
    const userData = this.getUserData();
    if (!userData) return;

    // Update enrollment count
    userData.enrolledCoursesCount = (userData.enrolledCoursesCount || 0) + 1;
    
    // Update enrolled course IDs
    userData.enrolledCourseIds = userData.enrolledCourseIds || [];
    if (!userData.enrolledCourseIds.includes(courseId)) {
      userData.enrolledCourseIds.push(courseId);
    }

    // Save updated user data
    localStorage.setItem("upskilleo-user", JSON.stringify(userData));
  }

  /**
   * Set user data (used after login)
   */
  setUserData(userData: UserData): void {
    if (typeof window === "undefined") return;
    localStorage.setItem("upskilleo-user", JSON.stringify(userData));
  }

  /**
   * Update user data from API response (syncs with backend)
   */
  updateUserDataFromAPI(userData: UserData): void {
    if (typeof window === "undefined") return;
    
    // Merge with existing data to preserve any local-only fields
    const existingData = this.getUserData() || {};
    const updatedData = { ...existingData, ...userData };
    
    localStorage.setItem("upskilleo-user", JSON.stringify(updatedData));
  }

  /**
   * Clear user data (used after logout)
   */
  clearUserData(): void {
    if (typeof window === "undefined") return;
    localStorage.removeItem("upskilleo-user");
    localStorage.removeItem("token");
  }
}

const userService = new UserService();
export default userService; 