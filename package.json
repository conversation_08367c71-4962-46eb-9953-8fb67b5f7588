{"name": "upskilleo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.7", "@types/moment": "^2.11.29", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "moment": "^2.30.1", "motion": "^12.18.1", "next": "14.2.28", "react": "^18", "react-ace": "^14.0.1", "react-dom": "^18", "react-resizable-panels": "^3.0.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.28", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}